### Test User Menu Structure API
@baseUrl = http://localhost:3000/api/v1
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao

### Test with user who has menu assignments
GET {{baseUrl}}/user-menu-assignments/user/dab11ee1-d7c5-4e93-9a3e-3ac898f1b1d2/menu-structure
Authorization: Bearer {{token}}

###

### Test with user who has no assignments (should return empty menus array)
GET {{baseUrl}}/user-menu-assignments/user/3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367/menu-structure
Authorization: Bearer {{token}}

###

### Test with non-existent user (should return 404)
GET {{baseUrl}}/user-menu-assignments/user/00000000-0000-0000-0000-000000000000/menu-structure
Authorization: Bearer {{token}}

###

### Test with invalid UUID (should return 400)
GET {{baseUrl}}/user-menu-assignments/user/invalid-uuid/menu-structure
Authorization: Bearer {{token}}

###

### Create some test assignments first
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367",
  "roleId": "*************-4f16-9dc7-c29502997ff0",
  "menu": [
    {
      "id": "089b77e7-9b5a-42d0-bc17-96e698ea4c1c",
      "permissionIds": [
        "16aa76c6-e8ce-440b-ad56-1f8b8df8198c",
        "a371d638-9ded-4259-b457-5f849d72f082"
      ]
    }
  ],
  "subMenu": [
    {
      "id": "5e8817d8-9175-4fb9-b647-7686f00e0356",
      "permissionIds": [
        "d48e77d4-9b01-497a-b3fe-38378808f922"
      ]
    }
  ]
}

###

### Now test the menu structure for the user with assignments
GET {{baseUrl}}/user-menu-assignments/user/3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367/menu-structure
Authorization: Bearer {{token}}

###

### Test with curl command
### curl -X GET "http://localhost:3000/api/v1/user-menu-assignments/user/3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367/menu-structure" \
###   -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao"
