import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsBoolean, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

export enum EntityType {
  USERS = 'users',
  AFFILIATES = 'affiliates',
  BETS = 'bets',
  DEPOSITS = 'deposits',
  COSTS = 'costs',
  AD_PERFORMANCE = 'ad_performance',
}

export enum ExportFormat {
  CSV = 'csv',
  JSON = 'json',
  XLSX = 'xlsx',
}

export class ImportDataDto {
  @ApiProperty({
    description: 'Type of entity to import',
    enum: EntityType,
    example: EntityType.USERS,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'File to import (CSV, JSON, XLSX)',
  })
  file: any;

  @ApiPropertyOptional({
    description: 'Skip first row (header row)',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  skipFirstRow?: boolean = true;

  @ApiPropertyOptional({
    description: 'CSV delimiter',
    default: ',',
  })
  @IsOptional()
  @IsString()
  delimiter?: string = ',';

  @ApiPropertyOptional({
    description: 'File encoding',
    default: 'utf8',
  })
  @IsOptional()
  @IsString()
  encoding?: string = 'utf8';

  @ApiPropertyOptional({
    description: 'Excel sheet name (for XLSX files). If not specified, first sheet will be used.',
    example: 'Sheet1',
  })
  @IsOptional()
  @IsString()
  sheetName?: string;
}

export class ExportDataDto {
  @ApiProperty({
    description: 'Type of entity to export',
    enum: EntityType,
    example: EntityType.USERS,
  })
  @IsEnum(EntityType)
  entityType: EntityType;

  @ApiPropertyOptional({
    description: 'Export format',
    enum: ExportFormat,
    default: ExportFormat.CSV,
  })
  @IsOptional()
  @IsEnum(ExportFormat)
  format?: ExportFormat = ExportFormat.CSV;

  @ApiPropertyOptional({
    description: 'Include headers in export',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  includeHeaders?: boolean = true;

  @ApiPropertyOptional({
    description: 'CSV delimiter for export',
    default: ',',
  })
  @IsOptional()
  @IsString()
  delimiter?: string = ',';

  @ApiPropertyOptional({
    description: 'Excel sheet name (for XLSX exports)',
    default: 'Sheet1',
    example: 'Data Export',
  })
  @IsOptional()
  @IsString()
  sheetName?: string = 'Sheet1';

  @ApiPropertyOptional({
    description: 'Filter by active status',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by department ID',
  })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiPropertyOptional({
    description: 'Filter by team ID',
  })
  @IsOptional()
  @IsString()
  teamId?: string;

  @ApiPropertyOptional({
    description: 'Filter records created from this date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter records created to this date',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  createdTo?: string;
}

export class JobStatusDto {
  @ApiProperty({
    description: 'Job ID',
    example: '12345',
  })
  @IsString()
  jobId: string;

  @ApiProperty({
    description: 'Queue name',
    example: 'import-export',
  })
  @IsString()
  queueName: string;
}

export class ImportResultDto {
  @ApiProperty({
    description: 'Job ID for tracking',
    example: '12345',
  })
  jobId: string | number;

  @ApiProperty({
    description: 'Entity type that was imported',
    enum: EntityType,
  })
  entityType: EntityType;

  @ApiProperty({
    description: 'Import status',
    example: 'queued',
  })
  status: string;

  @ApiProperty({
    description: 'Estimated processing time in minutes',
    example: 5,
  })
  estimatedTime: number;

  @ApiProperty({
    description: 'Message describing the import job',
    example: 'Import job has been queued successfully',
  })
  message: string;
}

export class ExportResultDto {
  @ApiProperty({
    description: 'Job ID for tracking',
    example: '12345',
  })
  jobId: string | number;

  @ApiProperty({
    description: 'Entity type that was exported',
    enum: EntityType,
  })
  entityType: EntityType;

  @ApiProperty({
    description: 'Export format',
    enum: ExportFormat,
  })
  format: ExportFormat;

  @ApiProperty({
    description: 'Export status',
    example: 'queued',
  })
  status: string;

  @ApiProperty({
    description: 'Estimated processing time in minutes',
    example: 3,
  })
  estimatedTime: number;

  @ApiProperty({
    description: 'Message describing the export job',
    example: 'Export job has been queued successfully',
  })
  message: string;
}

export class JobProgressDto {
  @ApiProperty({
    description: 'Job ID',
    example: '12345',
  })
  id: string | number;

  @ApiProperty({
    description: 'Job name/type',
    example: 'import-users',
  })
  name: string;

  @ApiProperty({
    description: 'Job status',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 75,
  })
  progress: number;

  @ApiProperty({
    description: 'Job data',
  })
  data: any;

  @ApiProperty({
    description: 'Number of attempts made',
    example: 1,
  })
  attemptsMade: number;

  @ApiProperty({
    description: 'Job creation timestamp',
    example: 1640995200000,
  })
  timestamp: number;

  @ApiPropertyOptional({
    description: 'Error message if job failed',
  })
  failedReason?: string;

  @ApiPropertyOptional({
    description: 'Job result if completed',
  })
  result?: any;

  @ApiPropertyOptional({
    description: 'Job completion timestamp',
  })
  finishedOn?: number;
}

export class QueueStatsDto {
  @ApiProperty({
    description: 'Queue name',
    example: 'import-export',
  })
  name: string;

  @ApiProperty({
    description: 'Job counts by status',
    example: {
      waiting: 5,
      active: 2,
      completed: 100,
      failed: 3,
      delayed: 0,
      paused: 0,
    },
  })
  counts: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  };

  @ApiProperty({
    description: 'Recent jobs by status',
    type: 'object',
    additionalProperties: true,
  })
  jobs: {
    waiting: JobProgressDto[];
    active: JobProgressDto[];
    completed: JobProgressDto[];
    failed: JobProgressDto[];
  };
}

export class AllQueuesStatsDto {
  @ApiProperty({
    description: 'Statistics for all queues',
    type: [QueueStatsDto],
  })
  queues: QueueStatsDto[];

  @ApiProperty({
    description: 'Summary statistics',
    example: {
      totalQueues: 4,
      totalJobs: 150,
      totalWaiting: 10,
      totalActive: 5,
      totalCompleted: 120,
      totalFailed: 15,
    },
  })
  summary: {
    totalQueues: number;
    totalJobs: number;
    totalWaiting: number;
    totalActive: number;
    totalCompleted: number;
    totalFailed: number;
  };
}
