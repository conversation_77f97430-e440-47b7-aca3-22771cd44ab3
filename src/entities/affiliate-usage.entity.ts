import { Entity, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Affiliate } from './affiliate.entity';
import { User } from './user.entity';

export enum AffiliateUsageStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export enum AffiliateUsageType {
  REGISTRATION = 'registration',
  PURCHASE = 'purchase',
  REFERRAL = 'referral',
  PROMOTION = 'promotion',
  OTHER = 'other',
}

/**
 * AffiliateUsage Entity
 * Tracks every time an affiliate code is used
 */
@Entity('affiliate_usages')
@Index(['affiliateId'])
@Index(['userId'])
@Index(['status'])
@Index(['usageType'])
@Index(['createdAt'])
@Index(['ipAddress'])
export class AffiliateUsage extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'Affiliate ID that was used',
  })
  affiliateId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User ID who used the affiliate code (if registered)',
  })
  userId?: string;

  @Column({
    type: 'enum',
    enum: AffiliateUsageStatus,
    default: AffiliateUsageStatus.PENDING,
    comment: 'Status of the affiliate usage',
  })
  status: AffiliateUsageStatus;

  @Column({
    type: 'enum',
    enum: AffiliateUsageType,
    default: AffiliateUsageType.REFERRAL,
    comment: 'Type of affiliate usage',
  })
  usageType: AffiliateUsageType;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address of the user who used the affiliate code',
  })
  ipAddress?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent string',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'Country code derived from IP address',
  })
  countryCode?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'City derived from IP address',
  })
  city?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Referrer URL where the affiliate code was used',
  })
  referrerUrl?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Landing page URL after using affiliate code',
  })
  landingUrl?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'UTM source parameter',
  })
  utmSource?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'UTM medium parameter',
  })
  utmMedium?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'UTM campaign parameter',
  })
  utmCampaign?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Commission amount for this usage (if applicable)',
  })
  commissionAmount?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Transaction value associated with this usage',
  })
  transactionValue?: number;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'Currency code for commission and transaction amounts',
  })
  currency?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Date when the usage was converted (e.g., registration completed)',
  })
  convertedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes about this usage',
  })
  notes?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata for this usage',
  })
  declare metadata?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Session ID to track user session',
  })
  sessionId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Device type (mobile, desktop, tablet)',
  })
  deviceType?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Browser name and version',
  })
  browser?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Operating system',
  })
  operatingSystem?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this usage resulted in a successful conversion',
  })
  isConverted: boolean;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Time spent on site in seconds (if tracked)',
  })
  timeOnSite?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Number of pages viewed in this session',
  })
  pageViews?: number;

  // Relationships
  @ManyToOne(() => Affiliate, (affiliate) => affiliate.id)
  @JoinColumn({ name: 'affiliateId' })
  affiliate: Affiliate;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'userId' })
  user?: User;

  // Helper methods
  get isSuccessful(): boolean {
    return this.status === AffiliateUsageStatus.SUCCESS;
  }

  get isPending(): boolean {
    return this.status === AffiliateUsageStatus.PENDING;
  }

  get isFailed(): boolean {
    return this.status === AffiliateUsageStatus.FAILED;
  }

  get conversionTime(): number | null {
    if (!this.convertedAt) return null;
    return this.convertedAt.getTime() - this.createdAt.getTime();
  }
}
