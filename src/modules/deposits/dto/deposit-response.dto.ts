import { ApiProperty } from '@nestjs/swagger';

export class DepositResponseDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  amount: number;

  @ApiProperty()
  transaction_id: string;

  @ApiProperty()
  payment_method: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  processed_at: Date;

  @ApiProperty()
  notes: string;

  @ApiProperty()
  external_reference: string;

  @ApiProperty()
  department_id: number;

  @ApiProperty()
  department: {
    id: number;
    name: string;
  };

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
} 