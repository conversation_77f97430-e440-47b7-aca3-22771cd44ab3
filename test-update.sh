#!/bin/bash

# Test updateBulkAssign with the menu assignment ID from previous response
curl -X PATCH \
  'http://localhost:3000/api/v1/user-menu-assignments/5a6b253a-2cf6-4131-a04b-3fe57de68190/bulk-assign' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
  -H 'Content-Type: application/json' \
  -d '{
    "userId": "3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367",
    "roleId": "97227721-8068-4f16-9dc7-c29502997ff0",
    "menu": [
      {
        "id": "089b77e7-9b5a-42d0-bc17-96e698ea4c1c",
        "permissionIds": [
          "a371d638-9ded-4259-b457-5f849d72f082"
        ]
      }
    ]
  }'
