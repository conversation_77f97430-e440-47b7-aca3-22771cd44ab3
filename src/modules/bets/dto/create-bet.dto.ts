import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsString, IsOptional, IsDateString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBetDto {
  @ApiProperty({ description: 'Số tiền cược' })
  @IsNotEmpty()
  @IsNumber()
  bet_amount: number;

  @ApiProperty({ description: 'Số tiền thắng' })
  @IsOptional()
  @IsNumber()
  win_amount?: number;

  @ApiProperty({ description: 'Số tiền thua' })
  @IsOptional()
  @IsNumber()
  loss_amount?: number;

  @ApiProperty({ description: 'ID cược' })
  @IsNotEmpty()
  @IsString()
  bet_id: string;

  @ApiProperty({ description: 'Loại game' })
  @IsNotEmpty()
  @IsString()
  game_type: string;

  @ApiProperty({ description: 'Tên game' })
  @IsNotEmpty()
  @IsString()
  game_name: string;

  @ApiProperty({ description: 'Tr<PERSON>ng thái cược', enum: ['pending', 'won', 'lost', 'cancelled'] })
  @IsOptional()
  @IsEnum(['pending', 'won', 'lost', 'cancelled'])
  status?: string;

  @ApiProperty({ description: 'Thời gian kết thúc' })
  @IsOptional()
  @IsDateString()
  settled_at?: Date;

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: 'Tham chiếu bên ngoài' })
  @IsOptional()
  @IsString()
  external_reference?: string;

  @ApiProperty({ description: 'ID bộ phận' })
  @IsNotEmpty()
  @IsString()
  department_id: string;
} 