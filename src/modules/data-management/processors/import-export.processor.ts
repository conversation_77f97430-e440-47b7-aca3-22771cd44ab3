import { Processor, Process } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bull';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { User, Affiliate, Bet, Deposit, AuditLog } from '@/entities';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { QUEUE_NAMES, JOB_TYPES } from '@/common/queue/queue.constants';

export interface ImportJobData {
  filePath: string;
  entityType: string;
  userId: number;
  options?: {
    skipFirstRow?: boolean;
    delimiter?: string;
    encoding?: string;
  };
}

export interface ExportJobData {
  entityType: string;
  userId: number;
  filters?: any;
  outputPath?: string;
  format?: 'csv' | 'json' | 'xlsx';
  options?: {
    includeHeaders?: boolean;
    delimiter?: string;
    encoding?: string;
    sheetName?: string;
  };
}

@Injectable()
@Processor(QUEUE_NAMES.IMPORT_EXPORT)
export class ImportExportProcessor {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Affiliate) private affiliateRepository: Repository<Affiliate>,
    @InjectRepository(Bet) private betRepository: Repository<Bet>,
    @InjectRepository(Deposit) private depositRepository: Repository<Deposit>,
    @InjectRepository(AuditLog) private auditLogRepository: Repository<AuditLog>,
    private readonly logger: CustomLoggerService,
  ) {}

  @Process(JOB_TYPES.IMPORT_USERS)
  async importUsers(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting user import from ${filePath}`, 'ImportExportProcessor');

      const users = await this.parseFile(filePath, options);
      const totalUsers = users.length;
      let processedUsers = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const userData of users) {
        try {
          // Validate required fields
          if (!userData.username || !userData.email) {
            throw new Error('Username and email are required');
          }

          // Check if user already exists
          const existingUser = await this.userRepository.findOne({
            where: [
              { username: userData.username },
              { email: userData.email }
            ]
          });

          if (existingUser) {
            throw new Error(`User with username ${userData.username} or email ${userData.email} already exists`);
          }

          // Create new user
          const user = this.userRepository.create({
            username: userData.username,
            email: userData.email,
            fullName: `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || userData.username,
            phone: userData.phone || null,
            password: 'temp_password', // This should be handled properly in real implementation
            // Add other fields as needed
          });

          await this.userRepository.save(user);
          successCount++;

          // Log audit trail
          const auditLog = this.auditLogRepository.create({
            userId: userId.toString(),
            action: 'import' as any,
            entityType: 'User',
            entityId: user.id,
            description: `Imported user: ${user.username}`,
            ipAddress: '127.0.0.1', // System import
          });
          await this.auditLogRepository.save(auditLog);

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedUsers + 1}: ${error.message}`);
          this.logger.warn(`Failed to import user at row ${processedUsers + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedUsers++;
        await job.progress(Math.round((processedUsers / totalUsers) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedUsers,
        successCount,
        errorCount,
        errors: errors.slice(0, 100), // Limit errors to prevent memory issues
      };

      this.logger.info(
        `User import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`User import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_USERS)
  async exportUsers(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting user export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.userRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.department', 'department')
        .leftJoinAndSelect('user.team', 'team');

      // Apply filters
      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('user.isActive = :isActive', { isActive: filters.isActive });
      }
      if (filters.departmentId) {
        queryBuilder.andWhere('user.departmentId = :departmentId', { departmentId: filters.departmentId });
      }
      if (filters.teamId) {
        queryBuilder.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('user.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('user.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const users = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `users_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      await this.exportToFile(users, filePath, format, 'user', options);

      await job.progress(100);

      // Log audit trail
      const auditLog3 = this.auditLogRepository.create({
        userId: userId.toString(),
        action: 'export' as any,
        entityType: 'User',
        description: `Exported ${users.length} users to ${fileName}`,
        ipAddress: '127.0.0.1',
      });
      await this.auditLogRepository.save(auditLog3);

      const result = {
        fileName,
        filePath,
        recordCount: users.length,
        format,
      };

      this.logger.info(
        `User export completed: ${users.length} records exported to ${fileName}`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`User export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.IMPORT_AFFILIATES)
  async importAffiliates(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting affiliate import from ${filePath}`, 'ImportExportProcessor');

      const affiliates = await this.parseFile(filePath, options);
      const totalAffiliates = affiliates.length;
      let processedAffiliates = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const affiliateData of affiliates) {
        try {
          // Validate required fields
          if (!affiliateData.code || !affiliateData.name) {
            throw new Error('Code and name are required');
          }

          // Check if affiliate already exists
          const existingAffiliate = await this.affiliateRepository.findOne({
            where: { code: affiliateData.code }
          });

          if (existingAffiliate) {
            throw new Error(`Affiliate with code ${affiliateData.code} already exists`);
          }

          // Create new affiliate
          const affiliate = this.affiliateRepository.create({
            code: affiliateData.code,
            userId: userId.toString(), // Required field
            description: affiliateData.description || affiliateData.name || '',
            commissionRate: parseFloat(affiliateData.commissionRate) || 0,
            isActive: affiliateData.isActive !== undefined ? affiliateData.isActive : true,
            // Add other fields as needed
          });

          await this.affiliateRepository.save(affiliate);
          successCount++;

          // Log audit trail
          const auditLog2 = this.auditLogRepository.create({
            userId: userId.toString(),
            action: 'import' as any,
            entityType: 'Affiliate',
            entityId: affiliate.id,
            description: `Imported affiliate: ${affiliate.code}`,
            ipAddress: '127.0.0.1',
          });
          await this.auditLogRepository.save(auditLog2);

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedAffiliates + 1}: ${error.message}`);
          this.logger.warn(`Failed to import affiliate at row ${processedAffiliates + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedAffiliates++;
        await job.progress(Math.round((processedAffiliates / totalAffiliates) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedAffiliates,
        successCount,
        errorCount,
        errors: errors.slice(0, 100),
      };

      this.logger.info(
        `Affiliate import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Affiliate import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_AFFILIATES)
  async exportAffiliates(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting affiliate export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.affiliateRepository.createQueryBuilder('affiliate');

      // Apply filters
      if (filters.isActive !== undefined) {
        queryBuilder.andWhere('affiliate.isActive = :isActive', { isActive: filters.isActive });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('affiliate.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('affiliate.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const affiliates = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `affiliates_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      await this.exportToFile(affiliates, filePath, format, 'affiliate', options);

      await job.progress(100);

      // Log audit trail
      const auditLog4 = this.auditLogRepository.create({
        userId: userId.toString(),
        action: 'export' as any,
        entityType: 'Affiliate',
        description: `Exported ${affiliates.length} affiliates to ${fileName}`,
        ipAddress: '127.0.0.1',
      });
      await this.auditLogRepository.save(auditLog4);

      const result = {
        fileName,
        filePath,
        recordCount: affiliates.length,
        format,
      };

      this.logger.info(
        `Affiliate export completed: ${affiliates.length} records exported to ${fileName}`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Affiliate export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.IMPORT_BETS)
  async importBets(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting bet import from ${filePath}`, 'ImportExportProcessor');

      const bets = await this.parseFile(filePath, options);
      const totalBets = bets.length;
      let processedBets = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const betData of bets) {
        try {
          // Validate required fields
          if (!betData.departmentId || !betData.bet_amount) {
            throw new Error('DepartmentId and bet_amount are required');
          }

          // Create new bet
          const bet = this.betRepository.create({
            department_id: betData.departmentId.toString(),
            bet_amount: parseFloat(betData.bet_amount) || 0,
            win_amount: parseFloat(betData.win_amount) || 0,
            loss_amount: parseFloat(betData.loss_amount) || 0,
            bet_id: betData.bet_id,
            game_type: betData.game_type || 'unknown',
            game_name: betData.game_name || 'unknown',
            status: betData.status || 'pending',
            notes: betData.notes,
            external_reference: betData.external_reference,
            // Add other fields as needed
          });

          await this.betRepository.save(bet);
          successCount++;

          // Log audit trail
          const auditLog = this.auditLogRepository.create({
            userId: userId.toString(),
            action: 'import' as any,
            entityType: 'Bet',
            entityId: bet.id.toString(),
            description: `Imported bet: ${bet.id}`,
            ipAddress: '127.0.0.1',
          });
          await this.auditLogRepository.save(auditLog);

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedBets + 1}: ${error.message}`);
          this.logger.warn(`Failed to import bet at row ${processedBets + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedBets++;
        await job.progress(Math.round((processedBets / totalBets) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedBets,
        successCount,
        errorCount,
        errors: errors.slice(0, 100),
      };

      this.logger.info(
        `Bet import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Bet import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_BETS)
  async exportBets(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting bet export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.betRepository.createQueryBuilder('bet');

      // Apply filters
      if (filters.status) {
        queryBuilder.andWhere('bet.status = :status', { status: filters.status });
      }
      if (filters.betType) {
        queryBuilder.andWhere('bet.betType = :betType', { betType: filters.betType });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('bet.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('bet.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const bets = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `bets_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      await this.exportToFile(bets, filePath, format, 'bet', options);

      await job.progress(100);

      // Log audit trail
      const auditLog = this.auditLogRepository.create({
        userId: userId.toString(),
        action: 'export' as any,
        entityType: 'Bet',
        entityId: 'bulk',
        description: `Exported ${bets.length} bets to ${format.toUpperCase()}`,
        ipAddress: '127.0.0.1',
      });
      await this.auditLogRepository.save(auditLog);

      const result = {
        totalExported: bets.length,
        filePath,
        format,
        fileSize: fs.statSync(filePath).size,
      };

      this.logger.info(
        `Bet export completed: ${bets.length} records exported`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Bet export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.IMPORT_DEPOSITS)
  async importDeposits(job: Job<ImportJobData>) {
    const { filePath, userId, options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info(`Starting deposit import from ${filePath}`, 'ImportExportProcessor');

      const deposits = await this.parseFile(filePath, options);
      const totalDeposits = deposits.length;
      let processedDeposits = 0;
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const depositData of deposits) {
        try {
          // Validate required fields
          if (!depositData.amount || !depositData.transaction_id) {
            throw new Error('Amount and transaction_id are required');
          }

          // Create new deposit
          const deposit = this.depositRepository.create({
            user_id: depositData.user_id || null,
            department_id: parseInt(depositData.department_id) || 1,
            amount: parseFloat(depositData.amount) || 0,
            transaction_id: depositData.transaction_id,
            payment_method: depositData.payment_method || 'bank_transfer',
            status: depositData.status || 'pending',
            notes: depositData.notes,
            external_reference: depositData.external_reference,
            // Add other fields as needed
          });

          await this.depositRepository.save(deposit);
          successCount++;

          // Log audit trail
          const auditLog = this.auditLogRepository.create({
            userId: userId.toString(),
            action: 'import' as any,
            entityType: 'Deposit',
            entityId: deposit.id,
            description: `Imported deposit: ${deposit.id}`,
            ipAddress: '127.0.0.1',
          });
          await this.auditLogRepository.save(auditLog);

        } catch (error) {
          errorCount++;
          errors.push(`Row ${processedDeposits + 1}: ${error.message}`);
          this.logger.warn(`Failed to import deposit at row ${processedDeposits + 1}: ${error.message}`, 'ImportExportProcessor');
        }

        processedDeposits++;
        await job.progress(Math.round((processedDeposits / totalDeposits) * 100));
      }

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      const result = {
        totalProcessed: processedDeposits,
        successCount,
        errorCount,
        errors: errors.slice(0, 100),
      };

      this.logger.info(
        `Deposit import completed: ${successCount} success, ${errorCount} errors`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Deposit import failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  @Process(JOB_TYPES.EXPORT_DEPOSITS)
  async exportDeposits(job: Job<ExportJobData>) {
    const { userId, filters = {}, outputPath, format = 'csv', options = {} } = job.data;
    
    try {
      await job.progress(0);
      this.logger.info('Starting deposit export', 'ImportExportProcessor');

      // Build query
      const queryBuilder = this.depositRepository.createQueryBuilder('deposit');

      // Apply filters
      if (filters.status) {
        queryBuilder.andWhere('deposit.status = :status', { status: filters.status });
      }
      if (filters.method) {
        queryBuilder.andWhere('deposit.method = :method', { method: filters.method });
      }
      if (filters.createdFrom) {
        queryBuilder.andWhere('deposit.createdAt >= :createdFrom', { createdFrom: filters.createdFrom });
      }
      if (filters.createdTo) {
        queryBuilder.andWhere('deposit.createdAt <= :createdTo', { createdTo: filters.createdTo });
      }

      const deposits = await queryBuilder.getMany();
      await job.progress(50);

      // Generate output file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `deposits_export_${timestamp}.${format}`;
      const filePath = outputPath || path.join('./uploads/exports', fileName);

      // Ensure export directory exists
      const exportDir = path.dirname(filePath);
      if (!fs.existsSync(exportDir)) {
        fs.mkdirSync(exportDir, { recursive: true });
      }

      await this.exportToFile(deposits, filePath, format, 'deposit', options);

      await job.progress(100);

      // Log audit trail
      const auditLog = this.auditLogRepository.create({
        userId: userId.toString(),
        action: 'export' as any,
        entityType: 'Deposit',
        entityId: 'bulk',
        description: `Exported ${deposits.length} deposits to ${format.toUpperCase()}`,
        ipAddress: '127.0.0.1',
      });
      await this.auditLogRepository.save(auditLog);

      const result = {
        totalExported: deposits.length,
        filePath,
        format,
        fileSize: fs.statSync(filePath).size,
      };

      this.logger.info(
        `Deposit export completed: ${deposits.length} records exported`,
        'ImportExportProcessor',
        result
      );

      return result;

    } catch (error) {
      this.logger.error(`Deposit export failed: ${error.message}`, error.stack, 'ImportExportProcessor');
      throw error;
    }
  }

  /**
   * Get headers for different entity types
   */
  private getEntityHeaders(entityType: string): string[] {
    switch (entityType.toLowerCase()) {
      case 'user':
        return [
          'id',
          'username', 
          'email',
          'fullName',
          'phone',
          'isActive',
          'department',
          'team',
          'createdAt',
          'updatedAt'
        ];
      case 'affiliate':
        return [
          'id',
          'code',
          'userId',
          'description',
          'commissionRate', 
          'isActive',
          'createdAt',
          'updatedAt'
        ];
      case 'bet':
        return [
          'id',
          'department_id',
          'bet_amount',
          'win_amount',
          'loss_amount',
          'bet_id',
          'game_type',
          'game_name',
          'status',
          'notes',
          'external_reference',
          'createdAt',
          'updatedAt'
        ];
      case 'deposit':
        return [
          'id',
          'user_id',
          'department_id',
          'amount',
          'transaction_id',
          'payment_method',
          'status',
          'notes',
          'external_reference',
          'createdAt',
          'updatedAt'
        ];
      default:
        return ['id', 'createdAt', 'updatedAt'];
    }
  }

  /**
   * Parse file (CSV, XLSX, JSON) and return array of objects
   */
  private async parseFile(filePath: string, options: any = {}): Promise<any[]> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const fileExtension = path.extname(filePath).toLowerCase();
    
    try {
      switch (fileExtension) {
        case '.xlsx':
        case '.xls':
          return this.parseExcelFile(filePath, options);
        case '.csv':
          return this.parseCSVFile(filePath, options);
        case '.json':
          return this.parseJSONFile(filePath);
        default:
          throw new Error(`Unsupported file format: ${fileExtension}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse file: ${error.message}`);
    }
  }

  /**
   * Parse Excel file using xlsx library
   */
  private parseExcelFile(filePath: string, options: any = {}): any[] {
    const { sheetName, skipFirstRow = true } = options;
    
    // Read the workbook
    const workbook = XLSX.readFile(filePath);
    
    // Get sheet name (use first sheet if not specified)
    const targetSheetName = sheetName || workbook.SheetNames[0];
    const worksheet = workbook.Sheets[targetSheetName];
    
    if (!worksheet) {
      throw new Error(`Sheet "${targetSheetName}" not found in Excel file`);
    }
    
    // Convert sheet to JSON with headers
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1, // Use array of arrays format first
      defval: '', // Default value for empty cells
    });
    
    if (jsonData.length === 0) {
      return [];
    }
    
    // Get headers from first row
    const headers = jsonData[0] as string[];
    const dataRows = skipFirstRow ? jsonData.slice(1) : jsonData.slice(0);
    
    // Convert to array of objects
    return dataRows.map((row: any[]) => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
  }

  /**
   * Parse CSV file using xlsx library
   */
  private parseCSVFile(filePath: string, options: any = {}): any[] {
    const { delimiter = ',', encoding = 'utf8', skipFirstRow = true } = options;
    
    // Read CSV file content
    const csvContent = fs.readFileSync(filePath, { encoding });
    
    // Use xlsx to parse CSV
    const workbook = XLSX.read(csvContent, {
      type: 'string',
      raw: false,
      FS: delimiter,
    });
    
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      defval: '',
    });
    
    if (jsonData.length === 0) {
      return [];
    }
    
    // Get headers from first row
    const headers = jsonData[0] as string[];
    const dataRows = skipFirstRow ? jsonData.slice(1) : jsonData.slice(0);
    
    // Convert to array of objects
    return dataRows.map((row: any[]) => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });
  }

  /**
   * Parse JSON file
   */
  private parseJSONFile(filePath: string): any[] {
    try {
      const jsonContent = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(jsonContent);
      
      if (!Array.isArray(data)) {
        throw new Error('JSON file must contain an array of objects');
      }
      
      return data;
    } catch (error) {
      throw new Error(`Failed to parse JSON file: ${error.message}`);
    }
  }

  /**
   * Export data to file using xlsx library
   */
  private async exportToFile(data: any[], filePath: string, format: string, entityType: string, options: any = {}): Promise<void> {
    const { includeHeaders = true, delimiter = ',', sheetName = 'Sheet1' } = options;

    if (data.length === 0) {
      // Create empty file with headers based on format
      const headers = this.getEntityHeaders(entityType);
      
      switch (format.toLowerCase()) {
        case 'xlsx':
          const emptyWorkbook = XLSX.utils.book_new();
          const headerRow = includeHeaders ? [headers] : [];
          const emptyWorksheet = XLSX.utils.aoa_to_sheet(headerRow);
          XLSX.utils.book_append_sheet(emptyWorkbook, emptyWorksheet, sheetName);
          XLSX.writeFile(emptyWorkbook, filePath);
          break;
        case 'csv':
          const csvHeaders = includeHeaders ? headers.join(delimiter) + '\n' : '';
          fs.writeFileSync(filePath, csvHeaders);
          break;
        case 'json':
          fs.writeFileSync(filePath, '[]', 'utf8');
          break;
      }
      return;
    }

    switch (format.toLowerCase()) {
      case 'xlsx':
        await this.exportToXLSX(data, filePath, entityType, { sheetName, includeHeaders });
        break;
      case 'csv':
        await this.exportToCSV(data, filePath, entityType, { delimiter, includeHeaders });
        break;
      case 'json':
        await this.exportToJSON(data, filePath);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Export data to Excel file using xlsx library
   */
  private async exportToXLSX(data: any[], filePath: string, entityType: string, options: any = {}): Promise<void> {
    const { sheetName = 'Sheet1', includeHeaders = true } = options;
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    
    // Convert data to worksheet
    let worksheet;
    if (data.length === 0 && includeHeaders) {
      // Create worksheet with just headers when data is empty
      const headers = this.getEntityHeaders(entityType);
      worksheet = XLSX.utils.aoa_to_sheet([headers]);
    } else {
      worksheet = XLSX.utils.json_to_sheet(data, {
        header: includeHeaders ? undefined : [], // Include headers by default
        skipHeader: !includeHeaders
      });
    }
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // Write file
    XLSX.writeFile(workbook, filePath);
  }

  /**
   * Export data to CSV file using xlsx library
   */
  private async exportToCSV(data: any[], filePath: string, entityType: string, options: any = {}): Promise<void> {
    const { delimiter = ',', includeHeaders = true } = options;
    
    // Create worksheet from JSON data
    let worksheet;
    if (data.length === 0 && includeHeaders) {
      // Create worksheet with just headers when data is empty
      const headers = this.getEntityHeaders(entityType);
      worksheet = XLSX.utils.aoa_to_sheet([headers]);
    } else {
      worksheet = XLSX.utils.json_to_sheet(data, {
        skipHeader: !includeHeaders
      });
    }
    
    // Convert worksheet to CSV
    const csvContent = XLSX.utils.sheet_to_csv(worksheet, {
      FS: delimiter, // Field separator
      RS: '\n', // Record separator
    });
    
    // Write to file
    fs.writeFileSync(filePath, csvContent, 'utf8');
  }

  /**
   * Export data to JSON file
   */
  private async exportToJSON(data: any[], filePath: string): Promise<void> {
    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonData, 'utf8');
  }
}
