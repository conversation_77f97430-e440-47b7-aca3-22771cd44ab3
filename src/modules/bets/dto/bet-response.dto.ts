import { ApiProperty } from '@nestjs/swagger';

export class BetResponseDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  bet_amount: number;

  @ApiProperty()
  win_amount: number;

  @ApiProperty()
  loss_amount: number;

  @ApiProperty()
  bet_id: string;

  @ApiProperty()
  game_type: string;

  @ApiProperty()
  game_name: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  settled_at: Date;

  @ApiProperty()
  notes: string;

  @ApiProperty()
  external_reference: string;

  @ApiProperty()
  department_id: number;

  @ApiProperty()
  department: {
    id: number;
    name: string;
  };

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
} 