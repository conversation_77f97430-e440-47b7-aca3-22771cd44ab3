import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { DataManagementController } from './data-management.controller';
import { DataManagementService } from './data-management.service';
import { UserAnalytics } from '../../entities/user-analytics.entity';
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Deposit } from '../../entities/deposit.entity';
import { Bet } from '../../entities/bet.entity';
import { Affiliate } from '../../entities/affiliate.entity';
import { AuditLog } from '../../entities/audit-log.entity';
import { AdPerformance } from '../../entities/ad-performance.entity';
import { Cost } from '../../entities/cost.entity';
import { CommonServicesModule } from '../../common/services/common-services.module';
import { QueueModule } from '../../common/queue';
import { QUEUE_NAMES } from '../../common/queue/queue.constants';
import { ImportExportProcessor } from './processors/import-export.processor';
import { FileProcessingProcessor } from './processors/file-processing.processor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserAnalytics,
      User,
      Department,
      Deposit,
      Bet,
      Affiliate,
      AuditLog,
      AdPerformance,
      Cost,
    ]),
    CommonServicesModule,
    QueueModule,
    BullModule.registerQueue(
      { name: QUEUE_NAMES.IMPORT_EXPORT },
      { name: QUEUE_NAMES.FILE_PROCESSING },
    ),
  ],
  controllers: [DataManagementController],
  providers: [
    DataManagementService,
    ImportExportProcessor,
    FileProcessingProcessor,
  ],
  exports: [DataManagementService],
})
export class DataManagementModule {}