#!/bin/bash

echo "=== Testing User Menu Assignments ==="

echo "1. Testing bulkAssign with different user..."
RESPONSE1=$(curl -s -X POST \
  'http://localhost:3000/api/v1/user-menu-assignments/bulk-assign' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
  -H 'Content-Type: application/json' \
  -d '{
    "userId": "dab11ee1-d7c5-4e93-9a3e-3ac898f1b1d2",
    "roleId": "97227721-8068-4f16-9dc7-c29502997ff0",
    "menu": [
      {
        "id": "089b77e7-9b5a-42d0-bc17-96e698ea4c1c",
        "permissionIds": [
          "16aa76c6-e8ce-440b-ad56-1f8b8df8198c"
        ]
      }
    ]
  }')

echo "Response: $RESPONSE1"
echo ""

# Extract assignment ID from response
ASSIGNMENT_ID=$(echo $RESPONSE1 | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
echo "Assignment ID: $ASSIGNMENT_ID"
echo ""

if [ -n "$ASSIGNMENT_ID" ]; then
  echo "2. Testing updateBulkAssign..."
  RESPONSE2=$(curl -s -X PATCH \
    "http://localhost:3000/api/v1/user-menu-assignments/$ASSIGNMENT_ID/bulk-assign" \
    -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
    -H 'Content-Type: application/json' \
    -d '{
      "userId": "dab11ee1-d7c5-4e93-9a3e-3ac898f1b1d2",
      "roleId": "97227721-8068-4f16-9dc7-c29502997ff0",
      "menu": [
        {
          "id": "089b77e7-9b5a-42d0-bc17-96e698ea4c1c",
          "permissionIds": [
            "a371d638-9ded-4259-b457-5f849d72f082"
          ]
        }
      ]
    }')
  
  echo "Response: $RESPONSE2"
  echo ""

  echo "3. Testing deleteBulk..."
  RESPONSE3=$(curl -s -X DELETE \
    'http://localhost:3000/api/v1/user-menu-assignments/bulk' \
    -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
    -H 'Content-Type: application/json' \
    -d "{
      \"ids\": [
        \"$ASSIGNMENT_ID\"
      ]
    }")
  
  echo "Response: $RESPONSE3"
  echo ""
else
  echo "Failed to create assignment, skipping update and delete tests"
fi

echo "=== Testing Complete ==="
