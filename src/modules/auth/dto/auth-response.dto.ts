import { ApiProperty } from '@nestjs/swagger';

export class MenuPermissionDto {
  @ApiProperty({ description: 'Permission ID' })
  id: string;

  @ApiProperty({ description: 'Permission name' })
  name: string;

  @ApiProperty({ description: 'Permission action' })
  action: string;

  @ApiProperty({ description: 'Permission description' })
  description: string;
}

export class SubMenuDto {
  @ApiProperty({ description: 'SubMenu ID' })
  id: string;

  @ApiProperty({ description: 'SubMenu title' })
  title: string;

  @ApiProperty({ description: 'SubMenu description' })
  description: string;

  @ApiProperty({ description: 'SubMenu link' })
  link: string;

  @ApiProperty({
    description: 'SubMenu permissions',
    type: [MenuPermissionDto]
  })
  permissions: MenuPermissionDto[];
}

export class MenuDto {
  @ApiProperty({ description: 'Menu ID' })
  id: string;

  @ApiProperty({ description: 'Menu title' })
  title: string;

  @ApiProperty({ description: 'Menu description' })
  description: string;

  @ApiProperty({
    description: 'Menu permissions',
    type: [MenuPermissionDto]
  })
  permissions: MenuPermissionDto[];

  @ApiProperty({
    description: 'Submenus under this menu',
    type: [SubMenuDto]
  })
  submenus: SubMenuDto[];
}

export class UserProfileDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'Username' })
  username: string;

  @ApiProperty({ description: 'Email' })
  email: string;

  @ApiProperty({ description: 'Full name' })
  fullName: string;

  @ApiProperty({ description: 'User level', nullable: true })
  level: number | null;

  @ApiProperty({ description: 'User roles', type: [String] })
  roles: string[];

  @ApiProperty({ description: 'User permissions', type: [String] })
  permissions: string[];

  @ApiProperty({ description: 'User departments', type: [String] })
  department: string[];

  @ApiProperty({ description: 'User teams', type: [String] })
  team: string[];

  @ApiProperty({
    description: 'User accessible menus with nested submenus',
    type: [MenuDto]
  })
  menus: MenuDto[];
}

export class LoginResponseDto {
  @ApiProperty({ description: 'JWT access token' })
  access_token: string;

  @ApiProperty({ 
    description: 'User profile information',
    type: UserProfileDto
  })
  user: UserProfileDto;
}
