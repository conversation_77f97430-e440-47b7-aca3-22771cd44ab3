import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { User } from '@/entities/user.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';
import { Permission } from '@/entities/permission.entity';
import {
  CreateUserMenuAssignmentDto,
  UpdateUserMenuAssignmentDto,
  UserMenuAssignmentResponseDto,
  BulkAssignMenuDto,
  UserMenuQueryDto,
  UserMenuStructureResponseDto,
  MenuListResponseDto
} from './dto';
import { PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { Department } from '@/entities/department.entity';
import { Team } from '@/entities/team.entity';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';
import { Role } from '@/entities/role.entity';

@Injectable()
export class UserMenuAssignmentsService {
  constructor(
    @InjectRepository(UserMenuAssignment)
    private readonly userMenuAssignmentRepository: Repository<UserMenuAssignment>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Menu)
    private readonly menuRepository: Repository<Menu>,
    @InjectRepository(SubMenu)
    private readonly submenuRepository: Repository<SubMenu>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    @InjectRepository(UserRoleAssignment)
    private readonly userRoleAssignmentRepository: Repository<UserRoleAssignment>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async create(createDto: CreateUserMenuAssignmentDto, createdBy?: string): Promise<UserMenuAssignmentResponseDto> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: createDto.userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Validate department exists
    if (createDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: createDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team exists
    if (createDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: createDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    // Validate role exists
    if (createDto.roleId) {
      const role = await this.roleRepository.findOne({ where: { id: createDto.roleId } });
      if (!role) {
        throw new BadRequestException('Role not found');
      }
    }

    // Validate menu exists
    const menu = await this.menuRepository.findOne({ where: { id: createDto.menuId } });
    if (!menu) {
      throw new BadRequestException('Menu not found');
    }

    // Validate submenu if provided
    if (createDto.submenuId) {
      const submenu = await this.submenuRepository.findOne({ 
        where: { id: createDto.submenuId },
        relations: ['menu']
      });
      if (!submenu) {
        throw new BadRequestException('SubMenu not found');
      }
      
      // Check if submenu belongs to the specified menu
      if (submenu.menu.id !== createDto.menuId) {
        throw new BadRequestException('SubMenu does not belong to the specified menu');
      }
    }

    // Validate permissions exist
    const permissions = await this.permissionRepository.find({
      where: { id: In(createDto.permissionIds) }
    });
    if (permissions.length !== createDto.permissionIds.length) {
      throw new BadRequestException('One or more permissions not found');
    }

    // Check if assignment already exists
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereCondition: any = {
      userId: createDto.userId,
      menuId: createDto.menuId,
    };

    if (createDto.submenuId) {
      whereCondition.submenuId = createDto.submenuId;
    } else {
      whereCondition.submenuId = IsNull();
    }

    const existingAssignment = await this.userMenuAssignmentRepository.findOne({
      where: whereCondition,
    });

    if (existingAssignment) {
      throw new ConflictException('User menu assignment already exists');
    }

    const whereConditionRole: {
      userId: string;
      roleId: string;
      departmentId?: string;
      teamId?: string;
    } = {
      userId: createDto.userId,
      roleId: createDto.roleId,
    };

    if (createDto.departmentId) {
      whereConditionRole.departmentId = createDto.departmentId;
    }

    if (createDto.teamId) {
      whereConditionRole.teamId = createDto.teamId;
    }

    const existingRoleAssignment = await this.userRoleAssignmentRepository.findOne({
      where: whereConditionRole,
    });

    if (existingRoleAssignment) {
      throw new ConflictException('User role assignment already exists');
    }

    // Create user role assignment
    const userRoleAssignment = this.userRoleAssignmentRepository.create({
      userId: createDto.userId,
      roleId: createDto.roleId,
      departmentId: createDto.departmentId,
      teamId: createDto.teamId,
      createdBy,
    });

    await this.userRoleAssignmentRepository.save(userRoleAssignment);

    // Create assignment
    const assignment = this.userMenuAssignmentRepository.create({
      userId: createDto.userId,
      menuId: createDto.menuId,
      submenuId: createDto.submenuId,
      permissions,
      createdBy,
    });

    const savedAssignment = await this.userMenuAssignmentRepository.save(assignment);
    return this.transformToResponseDto(savedAssignment);
  }

  async bulkAssign(bulkAssignDto: BulkAssignMenuDto, createdBy?: string): Promise<UserMenuAssignmentResponseDto[]> {
    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: bulkAssignDto.userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const assignments: UserMenuAssignment[] = [];

    // Process menu assignments
    if (bulkAssignDto.menu && bulkAssignDto.menu.length > 0) {
      for (const menuAssignment of bulkAssignDto.menu) {
        // Validate menu exists
        const menu = await this.menuRepository.findOne({ where: { id: menuAssignment.id } });
        if (!menu) {
          throw new BadRequestException(`Menu not found: ${menuAssignment.id}`);
        }

        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(menuAssignment.permissionIds) }
        });
        if (permissions.length !== menuAssignment.permissionIds.length) {
          throw new BadRequestException(`One or more permissions not found for menu: ${menuAssignment.id}`);
        }

        // Check if assignment already exists
        const existingAssignment = await this.userMenuAssignmentRepository.findOne({
          where: {
            userId: bulkAssignDto.userId,
            menuId: menuAssignment.id,
            submenuId: IsNull(),
          },
        });

        if (!existingAssignment) {
          const assignment = this.userMenuAssignmentRepository.create({
            userId: bulkAssignDto.userId,
            menuId: menuAssignment.id,
            permissions,
            createdBy,
          });
          assignments.push(assignment);
        }
      }
    }

    // Process submenu assignments
    if (bulkAssignDto.subMenu && bulkAssignDto.subMenu.length > 0) {
      for (const submenuAssignment of bulkAssignDto.subMenu) {
        // Validate submenu exists and get its menu
        const submenu = await this.submenuRepository.findOne({
          where: { id: submenuAssignment.id },
          relations: ['menu']
        });
        if (!submenu) {
          throw new BadRequestException(`SubMenu not found: ${submenuAssignment.id}`);
        }

        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(submenuAssignment.permissionIds) }
        });
        if (permissions.length !== submenuAssignment.permissionIds.length) {
          throw new BadRequestException(`One or more permissions not found for submenu: ${submenuAssignment.id}`);
        }

        // Check if assignment already exists
        const existingAssignment = await this.userMenuAssignmentRepository.findOne({
          where: {
            userId: bulkAssignDto.userId,
            menuId: submenu.menu.id,
            submenuId: submenuAssignment.id,
          },
        });

        if (!existingAssignment) {
          const assignment = this.userMenuAssignmentRepository.create({
            userId: bulkAssignDto.userId,
            menuId: submenu.menu.id,
            submenuId: submenuAssignment.id,
            permissions,
            createdBy,
          });
          assignments.push(assignment);
        }
      }
    }

    // Process role assignments
    if (bulkAssignDto.roleId) {
      const role = await this.roleRepository.findOne({ where: { id: bulkAssignDto.roleId } });
      if (!role) {
        throw new BadRequestException('Role not found');
      }
    }

    // Validate department exists
    if (bulkAssignDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: bulkAssignDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team exists
    if (bulkAssignDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: bulkAssignDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    const whereConditionRole: {
      userId: string;
      roleId: string;
      departmentId?: string;
      teamId?: string;
    } = {
      userId: bulkAssignDto.userId,
      roleId: bulkAssignDto.roleId,
    };

    if (bulkAssignDto.departmentId) {
      whereConditionRole.departmentId = bulkAssignDto.departmentId;
    }
    
    if (bulkAssignDto.teamId) {
      whereConditionRole.teamId = bulkAssignDto.teamId;
    }
    
    // Check if role assignment already exists
    const existingRoleAssignment = await this.userRoleAssignmentRepository.findOne({
      where: whereConditionRole,
    });

    // Create user role assignment
    const userRoleAssignment = this.userRoleAssignmentRepository.create({
      userId: bulkAssignDto.userId,
      roleId: bulkAssignDto.roleId,
      departmentId: bulkAssignDto.departmentId,
      teamId: bulkAssignDto.teamId,
      createdBy,
    });

    if (existingRoleAssignment) {
      throw new ConflictException('User role assignment already exists');
    }

    await this.userRoleAssignmentRepository.save(userRoleAssignment);

    const savedAssignments = await this.userMenuAssignmentRepository.save(assignments);
    return savedAssignments.map(assignment => this.transformToResponseDto(assignment));
  }

  async updateBulkAssign(id: string, bulkAssignDto: BulkAssignMenuDto, updatedBy?: string): Promise<UserMenuAssignmentResponseDto> {
    // Find the existing assignment
    const existingAssignment = await this.userMenuAssignmentRepository.findOne({
      where: { id },
      relations: ['user', 'menu', 'submenu', 'permissions'],
    });

    if (!existingAssignment) {
      throw new NotFoundException(`User menu assignment with ID ${id} not found`);
    }

    // Validate user exists
    const user = await this.userRepository.findOne({ where: { id: bulkAssignDto.userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Validate department exists
    if (bulkAssignDto.departmentId) {
      const department = await this.departmentRepository.findOne({ where: { id: bulkAssignDto.departmentId } });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Validate team exists
    if (bulkAssignDto.teamId) {
      const team = await this.teamRepository.findOne({ where: { id: bulkAssignDto.teamId } });
      if (!team) {
        throw new BadRequestException('Team not found');
      }
    }

    // Validate role exists
    if (bulkAssignDto.roleId) {
      const role = await this.roleRepository.findOne({ where: { id: bulkAssignDto.roleId } });
      if (!role) {
        throw new BadRequestException('Role not found');
      }
    }

    // Validate that request contains appropriate data for the assignment type
    if (!existingAssignment.submenuId && (!bulkAssignDto.menu || bulkAssignDto.menu.length === 0)) {
      throw new BadRequestException('Menu assignments required for menu-level assignment');
    }

    if (existingAssignment.submenuId && (!bulkAssignDto.subMenu || bulkAssignDto.subMenu.length === 0)) {
      throw new BadRequestException('SubMenu assignments required for submenu-level assignment');
    }

    // Update user ID
    existingAssignment.userId = bulkAssignDto.userId;

    // Process menu assignments - update permissions for the current assignment
    if (bulkAssignDto.menu && bulkAssignDto.menu.length > 0 && !existingAssignment.submenuId) {
      // This is a menu-level assignment, find the menu assignment that matches
      const menuAssignment = bulkAssignDto.menu.find(menu => menu.id === existingAssignment.menuId);

      if (menuAssignment) {
        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(menuAssignment.permissionIds) }
        });
        if (permissions.length !== menuAssignment.permissionIds.length) {
          throw new BadRequestException(`One or more permissions not found for menu: ${menuAssignment.id}`);
        }

        // Update permissions
        existingAssignment.permissions = permissions;
      } else {
        throw new BadRequestException(`Menu assignment for menu ID ${existingAssignment.menuId} not found in request`);
      }
    }

    // Process submenu assignments - update permissions for the current assignment
    if (bulkAssignDto.subMenu && bulkAssignDto.subMenu.length > 0 && existingAssignment.submenuId) {
      // This is a submenu-level assignment, find the submenu assignment that matches
      const submenuAssignment = bulkAssignDto.subMenu.find(submenu => submenu.id === existingAssignment.submenuId);

      if (submenuAssignment) {
        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(submenuAssignment.permissionIds) }
        });
        if (permissions.length !== submenuAssignment.permissionIds.length) {
          throw new BadRequestException(`One or more permissions not found for submenu: ${submenuAssignment.id}`);
        }

        // Update permissions
        existingAssignment.permissions = permissions;
      } else {
        throw new BadRequestException(`SubMenu assignment for submenu ID ${existingAssignment.submenuId} not found in request`);
      }
    }

    // Update role assignment if provided
    if (bulkAssignDto.roleId) {
      const whereConditionRole: {
        userId: string;
        roleId: string;
        departmentId?: string;
        teamId?: string;
      } = {
        userId: bulkAssignDto.userId,
        roleId: bulkAssignDto.roleId,
      };

      if (bulkAssignDto.departmentId) {
        whereConditionRole.departmentId = bulkAssignDto.departmentId;
      }

      if (bulkAssignDto.teamId) {
        whereConditionRole.teamId = bulkAssignDto.teamId;
      }

      // Check if role assignment exists
      const existingRoleAssignment = await this.userRoleAssignmentRepository.findOne({
        where: whereConditionRole,
      });

      if (!existingRoleAssignment) {
        // Create new role assignment
        const userRoleAssignment = this.userRoleAssignmentRepository.create({
          userId: bulkAssignDto.userId,
          roleId: bulkAssignDto.roleId,
          departmentId: bulkAssignDto.departmentId,
          teamId: bulkAssignDto.teamId,
          createdBy: updatedBy,
        });

        await this.userRoleAssignmentRepository.save(userRoleAssignment);
      }
    }

    // Update the assignment
    existingAssignment.updatedBy = updatedBy;
    const updatedAssignment = await this.userMenuAssignmentRepository.save(existingAssignment);

    return this.transformToResponseDto(updatedAssignment);
  }

  async findAll(queryDto: UserMenuQueryDto): Promise<PaginatedResult<UserMenuAssignmentResponseDto>> {
    const { page, limit, search, sortBy, sortOrder, userId, menuId, submenuId, permissionId } = queryDto;
    
    const queryBuilder = this.userMenuAssignmentRepository.createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.user', 'user')
      .leftJoinAndSelect('assignment.menu', 'menu')
      .leftJoinAndSelect('assignment.submenu', 'submenu')
      .leftJoinAndSelect('assignment.permissions', 'permissions');
    
    // Add filters
    if (userId) {
      queryBuilder.andWhere('assignment.userId = :userId', { userId });
    }
    
    if (menuId) {
      queryBuilder.andWhere('assignment.menuId = :menuId', { menuId });
    }
    
    if (submenuId) {
      queryBuilder.andWhere('assignment.submenuId = :submenuId', { submenuId });
    }
    
    if (permissionId) {
      queryBuilder.andWhere('permissions.id = :permissionId', { permissionId });
    }
    
    // Add search functionality
    if (search) {
      queryBuilder.andWhere(
        '(user.username LIKE :search OR user.fullName LIKE :search OR menu.title LIKE :search OR submenu.title LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`assignment.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [assignments, total] = await queryBuilder.getManyAndCount();

    const responseData = assignments.map(assignment => this.transformToResponseDto(assignment));
    return createPaginatedResult(responseData, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<UserMenuAssignmentResponseDto> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: { id },
      relations: ['user', 'menu', 'submenu', 'permissions'],
    });

    if (!assignment) {
      throw new NotFoundException(`User menu assignment with ID ${id} not found`);
    }

    return this.transformToResponseDto(assignment);
  }

  async update(id: string, updateDto: UpdateUserMenuAssignmentDto, updatedBy?: string): Promise<UserMenuAssignmentResponseDto> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: { id },
      relations: ['user', 'menu', 'submenu', 'permissions'],
    });

    if (!assignment) {
      throw new NotFoundException(`User menu assignment with ID ${id} not found`);
    }

    // Validate permissions if provided
    if (updateDto.permissionIds) {
      const permissions = await this.permissionRepository.find({
        where: { id: In(updateDto.permissionIds) }
      });
      if (permissions.length !== updateDto.permissionIds.length) {
        throw new BadRequestException('One or more permissions not found');
      }
      assignment.permissions = permissions;
    }

    // Update other fields
    Object.assign(assignment, { updatedBy });
    const updatedAssignment = await this.userMenuAssignmentRepository.save(assignment);

    return this.transformToResponseDto(updatedAssignment);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    await this.findOne(id); // Check if exists

    // Soft delete
    await this.userMenuAssignmentRepository.update(id, { deletedBy });
    await this.userMenuAssignmentRepository.softDelete(id);
  }

  async deleteBulk(ids: string[], deletedBy?: string): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new BadRequestException('No assignment IDs provided');
    }

    // Validate all assignments exist
    const assignments = await this.userMenuAssignmentRepository.find({
      where: { id: In(ids) },
    });

    if (assignments.length !== ids.length) {
      const foundIds = assignments.map(assignment => assignment.id);
      const notFoundIds = ids.filter(id => !foundIds.includes(id));
      throw new NotFoundException(`User menu assignments not found: ${notFoundIds.join(', ')}`);
    }

    // Soft delete all assignments
    await this.userMenuAssignmentRepository.update(
      { id: In(ids) },
      { deletedBy }
    );
    await this.userMenuAssignmentRepository.softDelete(ids);
  }

  /**
   * Check if user has specific permission for a menu
   */
  async userHasMenuPermission(userId: string, menuId: string, permissionName: string): Promise<boolean> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        menuId,
        submenuId: IsNull(),
      },
      relations: ['permissions'],
    });

    if (!assignment) {
      return false;
    }

    return assignment.permissions.some(permission => permission.action === permissionName);
  }

  /**
   * Check if user has specific permission for a submenu
   */
  async userHasSubmenuPermission(userId: string, submenuId: string, permissionName: string): Promise<boolean> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        submenuId,
      },
      relations: ['permissions'],
    });

    if (!assignment) {
      return false;
    }

    return assignment.permissions.some(permission => permission.action === permissionName);
  }

  /**
   * Get user's menu permissions
   */
  async getUserMenuPermissions(userId: string, menuId: string): Promise<Permission[]> {
    const assignment = await this.userMenuAssignmentRepository.findOne({
      where: {
        userId,
        menuId,
        submenuId: IsNull(),
      },
      relations: ['permissions'],
    });

    return assignment ? assignment.permissions : [];
  }

  /**
   * Get user's complete menu structure with permissions
   */
  async getUserMenuStructure(userId: string): Promise<UserMenuStructureResponseDto> {
    // Validate user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['department', 'team', 'role']
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get all user menu assignments with relations
    const assignments = await this.userMenuAssignmentRepository.find({
      where: { userId },
      relations: ['menu', 'submenu', 'permissions'],
      order: {
        menu: { title: 'ASC' },
        submenu: { title: 'ASC' }
      }
    });

    // Get user role assignments to get department, team, role info
    const roleAssignments = await this.userRoleAssignmentRepository.find({
      where: { userId }
    });

    // Build the response structure
    const menuMap = new Map();

    // Process assignments
    for (const assignment of assignments) {
      const menuId = assignment.menu.id;

      if (!menuMap.has(menuId)) {
        menuMap.set(menuId, {
          id: assignment.menu.id,
          title: assignment.menu.title,
          description: assignment.menu.description,
          permissions: [],
          submenus: []
        });
      }

      const menuData = menuMap.get(menuId);

      if (!assignment.submenuId) {
        // This is a menu-level assignment
        menuData.permissions = assignment.permissions.map(permission => ({
          id: permission.id,
          name: permission.name,
          action: permission.action,
          description: permission.description
        }));
      } else if (assignment.submenu) {
        // This is a submenu-level assignment
        const existingSubmenu = menuData.submenus.find(sub => sub.id === assignment.submenu!.id);
        if (!existingSubmenu) {
          menuData.submenus.push({
            id: assignment.submenu.id,
            title: assignment.submenu.title,
            description: assignment.submenu.description,
            link: assignment.submenu.link,
            permissions: assignment.permissions.map(permission => ({
              id: permission.id,
              name: permission.name,
              action: permission.action,
              description: permission.description
            }))
          });
        }
      }
    }

    // Get department, role, team info from role assignments and user
    let department: { id: string; name: string } | undefined = undefined;
    let role: { id: string; name: string } | undefined = undefined;
    let team: { id: string; name: string } | undefined = undefined;

    // First try to get from user's direct relations
    if (user.department) {
      department = {
        id: user.department.id,
        name: user.department.name
      };
    }

    if (user.role) {
      role = {
        id: user.role.id,
        name: user.role.name
      };
    }

    if (user.team) {
      team = {
        id: user.team.id,
        name: user.team.name
      };
    }

    // If not found in user, try to get from role assignments
    if (roleAssignments.length > 0 && (!department || !role || !team)) {
      const roleAssignment = roleAssignments[0]; // Take the first one

      if (!department && roleAssignment.departmentId) {
        const dept = await this.departmentRepository.findOne({
          where: { id: roleAssignment.departmentId }
        });
        if (dept) {
          department = {
            id: dept.id,
            name: dept.name
          };
        }
      }

      if (!role && roleAssignment.roleId) {
        const roleEntity = await this.roleRepository.findOne({
          where: { id: roleAssignment.roleId }
        });
        if (roleEntity) {
          role = {
            id: roleEntity.id,
            name: roleEntity.name
          };
        }
      }

      if (!team && roleAssignment.teamId) {
        const teamEntity = await this.teamRepository.findOne({
          where: { id: roleAssignment.teamId }
        });
        if (teamEntity) {
          team = {
            id: teamEntity.id,
            name: teamEntity.name
          };
        }
      }
    }

    return {
      department,
      role,
      team,
      menus: Array.from(menuMap.values())
    };
  }

  private transformToResponseDto(assignment: UserMenuAssignment): UserMenuAssignmentResponseDto {
    return {
      id: assignment.id,
      userId: assignment.userId,
      menuId: assignment.menuId,
      submenuId: assignment.submenuId,
      permissions: assignment.permissions ? assignment.permissions.map(permission => ({
        id: permission.id,
        name: permission.name,
        description: permission.description,
      })) : [],
      user: assignment.user ? {
        id: assignment.user.id,
        username: assignment.user.username,
        fullName: assignment.user.fullName,
        email: assignment.user.email,
      } : undefined,
      menu: assignment.menu ? {
        id: assignment.menu.id,
        title: assignment.menu.title,
        description: assignment.menu.description,
      } : undefined,
      submenu: assignment.submenu ? {
        id: assignment.submenu.id,
        title: assignment.submenu.title,
        description: assignment.submenu.description,
        link: assignment.submenu.link,
      } : undefined,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt,
      createdBy: assignment.createdBy,
      updatedBy: assignment.updatedBy,
    };
  }

  /**
   * Get all menus with their submenus (no permissions)
   */
  async getMenuList(): Promise<MenuListResponseDto> {
    // Get all menus with their submenus
    const menus = await this.menuRepository.find({
      relations: ['submenus'],
      order: {
        title: 'ASC',
        submenus: { title: 'ASC' }
      }
    });

    // Transform to response format
    const menuList = menus.map(menu => ({
      id: menu.id,
      title: menu.title,
      description: menu.description,
      submenus: menu.submenus.map(submenu => ({
        id: submenu.id,
        title: submenu.title,
        description: submenu.description,
        link: submenu.link
      }))
    }));

    return {
      menus: menuList
    };
  }
}
