import { IsOptional, IsString, IsEnum, IsDateString, IsU<PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RegistrationSource, MemberStatus, ActivityLevel, GeographicRegion } from '../../../entities/user-analytics.entity';

export class DataManagementReportQueryDto {
  @ApiPropertyOptional({ description: 'Ngày bắt đầu báo cáo', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON><PERSON> kết thúc báo cáo', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiPropertyOptional({ description: 'Lọc theo phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;

  @ApiPropertyOptional({ enum: RegistrationSource, description: 'Lọ<PERSON> theo nguồn đăng ký' })
  @IsOptional()
  @IsEnum(RegistrationSource)
  registration_source?: RegistrationSource;

  @ApiPropertyOptional({ enum: MemberStatus, description: 'Lọc theo trạng thái thành viên' })
  @IsOptional()
  @IsEnum(MemberStatus)
  member_status?: MemberStatus;

  @ApiPropertyOptional({ enum: GeographicRegion, description: 'Lọc theo khu vực địa lý' })
  @IsOptional()
  @IsEnum(GeographicRegion)
  region?: GeographicRegion;

  @ApiPropertyOptional({
    description: 'Khoảng thời gian tính toán',
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
    default: 'daily'
  })
  @IsOptional()
  @IsString()
  time_period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' = 'daily';

  @ApiPropertyOptional({
    description: 'Loại báo cáo',
    enum: ['registration', 'member_analytics', 'ftd_analysis', 'lifecycle', 'comprehensive'],
    default: 'comprehensive'
  })
  @IsOptional()
  @IsString()
  report_type?: 'registration' | 'member_analytics' | 'ftd_analysis' | 'lifecycle' | 'comprehensive' = 'comprehensive';

  @ApiPropertyOptional({ description: 'So sánh với kỳ trước' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  compare_previous?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm phân tích theo phòng ban' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_department_analysis?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm phân tích theo nguồn đăng ký' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_source_analysis?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm phân tích địa lý' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_geographic_analysis?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm top performers' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_top_performers?: boolean;

  @ApiPropertyOptional({ description: 'Số lượng top performers', minimum: 1, maximum: 50, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  top_performers_limit?: number = 10;
}

// Registration Report DTOs
export class RegistrationStatsDto {
  @ApiProperty({ description: 'Tổng số đăng ký' })
  total_registrations: number;

  @ApiProperty({ description: 'Số đăng ký hôm nay' })
  today_registrations: number;

  @ApiProperty({ description: 'Số đăng ký tuần này' })
  week_registrations: number;

  @ApiProperty({ description: 'Số đăng ký tháng này' })
  month_registrations: number;

  @ApiProperty({ description: 'Tỷ lệ tăng trưởng so với kỳ trước (%)' })
  growth_rate: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi từ visit sang đăng ký (%)' })
  conversion_rate: number;

  @ApiProperty({ description: 'Chi phí thu hút khách hàng trung bình' })
  avg_acquisition_cost: number;

  @ApiProperty({ description: 'Thời gian đăng ký trung bình (giây)' })
  avg_registration_time: number;

  @ApiProperty({ description: 'Tỷ lệ đăng ký thành công (%)' })
  success_rate: number;
}

export class RegistrationTimeSeriesDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số đăng ký' })
  registrations: number;

  @ApiProperty({ description: 'Số đăng ký thành công' })
  successful_registrations: number;

  @ApiProperty({ description: 'Tỷ lệ thành công (%)' })
  success_rate: number;

  @ApiProperty({ description: 'Chi phí thu hút khách hàng' })
  acquisition_cost: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi (%)' })
  conversion_rate: number;
}

export class RegistrationSourceAnalysisDto {
  @ApiProperty({ enum: RegistrationSource, description: 'Nguồn đăng ký' })
  source: RegistrationSource;

  @ApiProperty({ description: 'Tên nguồn' })
  source_name: string;

  @ApiProperty({ description: 'Số đăng ký' })
  registrations_count: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng số (%)' })
  percentage: number;

  @ApiProperty({ description: 'Chi phí thu hút trung bình' })
  avg_acquisition_cost: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi (%)' })
  conversion_rate: number;

  @ApiProperty({ description: 'Thời gian đăng ký trung bình (giây)' })
  avg_registration_time: number;

  @ApiProperty({ description: 'Tỷ lệ hoàn thành hồ sơ (%)' })
  profile_completion_rate: number;

  @ApiProperty({ description: 'Điểm chất lượng (ROI)' })
  quality_score: number;
}

// Member Analytics DTOs
export class MemberAnalyticsStatsDto {
  @ApiProperty({ description: 'Tổng số thành viên' })
  total_members: number;

  @ApiProperty({ description: 'Thành viên mới (30 ngày)' })
  new_members: number;

  @ApiProperty({ description: 'Thành viên hoạt động (7 ngày)' })
  active_members: number;

  @ApiProperty({ description: 'Thành viên có nguy cơ rời bỏ' })
  at_risk_members: number;

  @ApiProperty({ description: 'Thành viên có giá trị cao' })
  high_value_members: number;

  @ApiProperty({ description: 'Tỷ lệ hoạt động (%)' })
  activity_rate: number;

  @ApiProperty({ description: 'Tỷ lệ churn (%)' })
  churn_rate: number;

  @ApiProperty({ description: 'Giá trị lifetime trung bình' })
  avg_lifetime_value: number;

  @ApiProperty({ description: 'Điểm tương tác trung bình' })
  avg_engagement_score: number;

  @ApiProperty({ description: 'Thời gian online trung bình (phút/ngày)' })
  avg_daily_session_time: number;
}

export class MemberStatusDistributionDto {
  @ApiProperty({ enum: MemberStatus, description: 'Trạng thái thành viên' })
  status: MemberStatus;

  @ApiProperty({ description: 'Tên trạng thái' })
  status_name: string;

  @ApiProperty({ description: 'Số lượng thành viên' })
  count: number;

  @ApiProperty({ description: 'Tỷ lệ (%)' })
  percentage: number;

  @ApiProperty({ description: 'Giá trị lifetime trung bình' })
  avg_lifetime_value: number;

  @ApiProperty({ description: 'Thời gian ở trạng thái này (ngày)' })
  avg_days_in_status: number;
}

export class MemberActivityLevelDto {
  @ApiProperty({ enum: ActivityLevel, description: 'Mức độ hoạt động' })
  level: ActivityLevel;

  @ApiProperty({ description: 'Tên mức độ' })
  level_name: string;

  @ApiProperty({ description: 'Số lượng thành viên' })
  count: number;

  @ApiProperty({ description: 'Tỷ lệ (%)' })
  percentage: number;

  @ApiProperty({ description: 'Điểm tương tác trung bình' })
  avg_engagement_score: number;

  @ApiProperty({ description: 'Số phiên trung bình/ngày' })
  avg_daily_sessions: number;

  @ApiProperty({ description: 'Thời gian phiên trung bình (phút)' })
  avg_session_duration: number;
}

// FTD Analysis DTOs
export class FTDAnalysisStatsDto {
  @ApiProperty({ description: 'Tổng số FTD' })
  total_ftd: number;

  @ApiProperty({ description: 'Tổng số tiền FTD' })
  total_ftd_amount: number;

  @ApiProperty({ description: 'Số tiền FTD trung bình' })
  avg_ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (% đăng ký thành FTD)' })
  ftd_conversion_rate: number;

  @ApiProperty({ description: 'Thời gian trung bình từ đăng ký đến FTD (ngày)' })
  avg_registration_to_ftd_days: number;

  @ApiProperty({ description: 'FTD hôm nay' })
  today_ftd: number;

  @ApiProperty({ description: 'FTD tuần này' })
  week_ftd: number;

  @ApiProperty({ description: 'FTD tháng này' })
  month_ftd: number;

  @ApiProperty({ description: 'Tỷ lệ tăng trưởng FTD (%)' })
  ftd_growth_rate: number;
}

export class FTDTimeSeriesDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số FTD' })
  ftd_count: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  ftd_amount: number;

  @ApiProperty({ description: 'Số tiền FTD trung bình' })
  avg_ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftd_rate: number;

  @ApiProperty({ description: 'Thời gian trung bình đến FTD (ngày)' })
  avg_days_to_ftd: number;
}

export class FTDSourceAnalysisDto {
  @ApiProperty({ enum: RegistrationSource, description: 'Nguồn đăng ký' })
  source: RegistrationSource;

  @ApiProperty({ description: 'Tên nguồn' })
  source_name: string;

  @ApiProperty({ description: 'Số FTD' })
  ftd_count: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  total_ftd_amount: number;

  @ApiProperty({ description: 'Số tiền FTD trung bình' })
  avg_ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftd_conversion_rate: number;

  @ApiProperty({ description: 'Thời gian trung bình đến FTD (ngày)' })
  avg_days_to_ftd: number;

  @ApiProperty({ description: 'ROI trên FTD (%)' })
  ftd_roi: number;
}

// Geographic Analysis DTOs
export class GeographicAnalysisDto {
  @ApiProperty({ enum: GeographicRegion, description: 'Khu vực' })
  region: GeographicRegion;

  @ApiProperty({ description: 'Tên khu vực' })
  region_name: string;

  @ApiProperty({ description: 'Số đăng ký' })
  registrations: number;

  @ApiProperty({ description: 'Số FTD' })
  ftd_count: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftd_rate: number;

  @ApiProperty({ description: 'Giá trị lifetime trung bình' })
  avg_lifetime_value: number;

  @ApiProperty({ description: 'Điểm tương tác trung bình' })
  avg_engagement_score: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng số (%)' })
  percentage: number;
}

// Department Analysis DTOs
export class DepartmentAnalysisDto {
  @ApiProperty({ description: 'ID phòng ban' })
  department_id: string;

  @ApiProperty({ description: 'Tên phòng ban' })
  department_name: string;

  @ApiProperty({ description: 'Số đăng ký' })
  registrations: number;

  @ApiProperty({ description: 'Số FTD' })
  ftd_count: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  ftd_amount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftd_rate: number;

  @ApiProperty({ description: 'Chi phí thu hút khách hàng' })
  total_acquisition_cost: number;

  @ApiProperty({ description: 'ROI (%)' })
  roi: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng số (%)' })
  percentage: number;

  @ApiProperty({ description: 'Điểm hiệu quả' })
  efficiency_score: number;
}

// Top Performers DTOs
export class TopPerformerDto {
  @ApiProperty({ description: 'ID người dùng' })
  user_id: string;

  @ApiProperty({ description: 'Tên người dùng' })
  username: string;

  @ApiProperty({ description: 'Email' })
  email: string;

  @ApiProperty({ description: 'Ngày đăng ký' })
  registration_date: Date;

  @ApiProperty({ description: 'Giá trị lifetime' })
  lifetime_value: number;

  @ApiProperty({ description: 'Tổng tiền gửi' })
  total_deposits: number;

  @ApiProperty({ description: 'Số lần gửi tiền' })
  deposit_count: number;

  @ApiProperty({ description: 'Điểm tương tác' })
  engagement_score: number;

  @ApiProperty({ description: 'Tên phòng ban' })
  department_name: string;

  @ApiProperty({ description: 'Nguồn đăng ký' })
  registration_source: RegistrationSource;
}

// Comparison DTOs
export class DataManagementComparisonDto {
  @ApiProperty({ description: 'Kỳ hiện tại' })
  current_period: {
    registrations: RegistrationStatsDto;
    members: MemberAnalyticsStatsDto;
    ftd: FTDAnalysisStatsDto;
  };

  @ApiProperty({ description: 'Kỳ trước' })
  previous_period: {
    registrations: RegistrationStatsDto;
    members: MemberAnalyticsStatsDto;
    ftd: FTDAnalysisStatsDto;
  };

  @ApiProperty({ description: 'Tỷ lệ thay đổi đăng ký (%)' })
  registrations_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi thành viên hoạt động (%)' })
  active_members_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi FTD (%)' })
  ftd_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi doanh thu FTD (%)' })
  ftd_revenue_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi chi phí thu hút (%)' })
  acquisition_cost_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi ROI (%)' })
  roi_change_percentage: number;
}

// Main Report DTO
export class DataManagementReportDto {
  // Basic Stats
  @ApiPropertyOptional({ description: 'Thống kê đăng ký' })
  registration_stats?: RegistrationStatsDto;

  @ApiPropertyOptional({ description: 'Thống kê thành viên' })
  member_stats?: MemberAnalyticsStatsDto;

  @ApiPropertyOptional({ description: 'Thống kê FTD' })
  ftd_stats?: FTDAnalysisStatsDto;

  // Time Series Data
  @ApiPropertyOptional({ type: [RegistrationTimeSeriesDto], description: 'Dữ liệu đăng ký theo thời gian' })
  registration_time_series?: RegistrationTimeSeriesDto[];

  @ApiPropertyOptional({ type: [FTDTimeSeriesDto], description: 'Dữ liệu FTD theo thời gian' })
  ftd_time_series?: FTDTimeSeriesDto[];

  // Analysis Data
  @ApiPropertyOptional({ type: [RegistrationSourceAnalysisDto], description: 'Phân tích theo nguồn đăng ký' })
  source_analysis?: RegistrationSourceAnalysisDto[];

  @ApiPropertyOptional({ type: [MemberStatusDistributionDto], description: 'Phân bố trạng thái thành viên' })
  member_status_distribution?: MemberStatusDistributionDto[];

  @ApiPropertyOptional({ type: [MemberActivityLevelDto], description: 'Phân bố mức độ hoạt động' })
  activity_level_distribution?: MemberActivityLevelDto[];

  @ApiPropertyOptional({ type: [FTDSourceAnalysisDto], description: 'Phân tích FTD theo nguồn' })
  ftd_source_analysis?: FTDSourceAnalysisDto[];

  @ApiPropertyOptional({ type: [GeographicAnalysisDto], description: 'Phân tích địa lý' })
  geographic_analysis?: GeographicAnalysisDto[];

  @ApiPropertyOptional({ type: [DepartmentAnalysisDto], description: 'Phân tích theo phòng ban' })
  department_analysis?: DepartmentAnalysisDto[];

  @ApiPropertyOptional({ type: [TopPerformerDto], description: 'Top performers' })
  top_performers?: TopPerformerDto[];

  @ApiPropertyOptional({ type: DataManagementComparisonDto, description: 'So sánh với kỳ trước' })
  comparison?: DataManagementComparisonDto;

  @ApiProperty({ description: 'Thời gian tạo báo cáo' })
  generated_at: Date;

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  report_period: string;

  @ApiProperty({ description: 'Loại báo cáo' })
  report_type: string;

  @ApiProperty({ description: 'Bộ lọc áp dụng' })
  applied_filters: any;
} 