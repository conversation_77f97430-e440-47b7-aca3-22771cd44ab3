import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { AdPerformance, AdPlatform, AdStatus, AdType, AdObjective } from '../../entities/ad-performance.entity';
import { Department } from '../../entities/department.entity';
import { User } from '../../entities/user.entity';
import { CreateAdPerformanceDto } from './dto/create-ad-performance.dto';
import { UpdateAdPerformanceDto } from './dto/update-ad-performance.dto';
import { AdPerformanceQueryDto } from './dto/ad-performance-query.dto';
import { AdPerformanceResponseDto } from './dto/ad-performance-response.dto';
import { 
  AdPerformanceReportQueryDto, 
  AdPerformanceReportDto, 
  AdPerformanceStatsDto,
  AdPerformanceTimeSeriesDto,
  AdPerformancePlatformAnalysisDto,
  AdPerformanceDepartmentAnalysisDto,
  AdPerformanceTopPerformerDto,
  AdPerformanceComparisonDto
} from './dto/ad-performance-report.dto';
import { plainToClass } from 'class-transformer';
import { UserContextService } from '../../common/services/user-context.service';
import { createPaginatedResult } from '@/common';

@Injectable()
export class AdPerformanceService {
  constructor(
    @InjectRepository(AdPerformance)
    private readonly adPerformanceRepository: Repository<AdPerformance>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly userContextService: UserContextService,
  ) {}

  async create(createAdPerformanceDto: CreateAdPerformanceDto, currentUserId: string): Promise<AdPerformanceResponseDto> {
    
    // Validate department if provided
    if (createAdPerformanceDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: createAdPerformanceDto.department_id }
      });
      
      if (!department) {
        throw new NotFoundException(`Department with ID ${createAdPerformanceDto.department_id} not found`);
      }
    }

    // Validate date range
    const startDate = new Date(createAdPerformanceDto.start_date);
    const endDate = new Date(createAdPerformanceDto.end_date);
    
    if (startDate >= endDate) {
      throw new BadRequestException('End date must be after start date');
    }

    // Create new ad performance record
    const adPerformance = this.adPerformanceRepository.create({
      ...createAdPerformanceDto,
      start_date: startDate,
      end_date: endDate,
      report_date: new Date(createAdPerformanceDto.report_date),
      created_by: currentUserId,
      updated_by: currentUserId,
    });

    // Calculate metrics before saving
    adPerformance.updateCalculatedMetrics();

    const savedAdPerformance = await this.adPerformanceRepository.save(adPerformance);
    
    return this.transformToResponseDto(savedAdPerformance);
  }

  async findAll(query: AdPerformanceQueryDto): Promise<{
    data: AdPerformanceResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, ...filters } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createQueryBuilder(filters);
    
    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const transformedData = data.map(item => this.transformToResponseDto(item));
    return createPaginatedResult(transformedData, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<AdPerformanceResponseDto> {
    const adPerformance = await this.adPerformanceRepository.findOne({
      where: { id },
      relations: ['department', 'creator', 'updater'],
    });

    if (!adPerformance) {
      throw new NotFoundException(`Ad Performance with ID ${id} not found`);
    }

    return this.transformToResponseDto(adPerformance);
  }

  async update(id: string, updateAdPerformanceDto: UpdateAdPerformanceDto, currentUserId: string): Promise<AdPerformanceResponseDto> {
    
    const adPerformance = await this.adPerformanceRepository.findOne({
      where: { id },
      relations: ['department', 'creator', 'updater'],
    });

    if (!adPerformance) {
      throw new NotFoundException(`Ad Performance with ID ${id} not found`);
    }

    // Validate department if provided
    if (updateAdPerformanceDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateAdPerformanceDto.department_id }
      });
      
      if (!department) {
        throw new NotFoundException(`Department with ID ${updateAdPerformanceDto.department_id} not found`);
      }
    }

    // Validate date range if dates are being updated
    if (updateAdPerformanceDto.start_date || updateAdPerformanceDto.end_date) {
      const startDate = updateAdPerformanceDto.start_date 
        ? new Date(updateAdPerformanceDto.start_date)
        : adPerformance.start_date;
      const endDate = updateAdPerformanceDto.end_date 
        ? new Date(updateAdPerformanceDto.end_date)
        : adPerformance.end_date;
      
      if (startDate >= endDate) {
        throw new BadRequestException('End date must be after start date');
      }
    }

    // Update the record
    Object.assign(adPerformance, {
      ...updateAdPerformanceDto,
      ...(updateAdPerformanceDto.start_date && { start_date: new Date(updateAdPerformanceDto.start_date) }),
      ...(updateAdPerformanceDto.end_date && { end_date: new Date(updateAdPerformanceDto.end_date) }),
      ...(updateAdPerformanceDto.report_date && { report_date: new Date(updateAdPerformanceDto.report_date) }),
      updated_by: currentUserId,
    });

    // Recalculate metrics
    adPerformance.updateCalculatedMetrics();

    const updatedAdPerformance = await this.adPerformanceRepository.save(adPerformance);
    
    return this.transformToResponseDto(updatedAdPerformance);
  }

  async remove(id: string): Promise<void> {
    const adPerformance = await this.adPerformanceRepository.findOne({
      where: { id },
    });

    if (!adPerformance) {
      throw new NotFoundException(`Ad Performance with ID ${id} not found`);
    }

    await this.adPerformanceRepository.remove(adPerformance);
  }

  async generateReport(query: AdPerformanceReportQueryDto): Promise<AdPerformanceReportDto> {
    const { 
      start_date, 
      end_date, 
      time_period = 'daily',
      compare_previous = false,
      include_department_analysis = false,
      include_platform_analysis = false,
      include_top_performers = false,
      top_performers_limit = 10
    } = query;

    const baseQuery = this.adPerformanceRepository.createQueryBuilder('ad')
      .leftJoinAndSelect('ad.department', 'department')
      .leftJoinAndSelect('ad.creator', 'creator');

    // Apply filters
    this.applyReportFilters(baseQuery, query);

    // Get basic stats
    const stats = await this.calculateStats(baseQuery);

    // Get time series data
    const timeSeries = await this.getTimeSeries(baseQuery, time_period);

    // Initialize report
    const report: AdPerformanceReportDto = {
      stats,
      time_series: timeSeries,
      generated_at: new Date(),
      report_period: this.formatReportPeriod(start_date || '', end_date || ''),
      applied_filters: query,
    };

    // Add platform analysis if requested
    if (include_platform_analysis) {
      report.platform_analysis = await this.getPlatformAnalysis(baseQuery);
    }

    // Add department analysis if requested
    if (include_department_analysis) {
      report.department_analysis = await this.getDepartmentAnalysis(baseQuery);
    }

    // Add top performers if requested
    if (include_top_performers) {
      report.top_performers = await this.getTopPerformers(baseQuery, top_performers_limit);
    }

    // Add comparison if requested
    if (compare_previous) {
      report.comparison = await this.getComparison(query, stats);
    }

    return report;
  }

  async recalculateMetrics(id: string): Promise<AdPerformanceResponseDto> {
    const adPerformance = await this.adPerformanceRepository.findOne({
      where: { id },
      relations: ['department', 'creator', 'updater'],
    });

    if (!adPerformance) {
      throw new NotFoundException(`Ad Performance with ID ${id} not found`);
    }

    // Recalculate all metrics
    adPerformance.updateCalculatedMetrics();

    const updatedAdPerformance = await this.adPerformanceRepository.save(adPerformance);
    
    return this.transformToResponseDto(updatedAdPerformance);
  }

  async bulkRecalculateMetrics(filters?: Partial<AdPerformanceQueryDto>): Promise<{ updated: number }> {
    const queryBuilder = this.createQueryBuilder(filters || {});
    const adPerformances = await queryBuilder.getMany();

    let updated = 0;

    for (const adPerformance of adPerformances) {
      adPerformance.updateCalculatedMetrics();
      await this.adPerformanceRepository.save(adPerformance);
      updated++;
    }

    return { updated };
  }

  private createQueryBuilder(filters: Partial<AdPerformanceQueryDto>): SelectQueryBuilder<AdPerformance> {
    const queryBuilder = this.adPerformanceRepository
      .createQueryBuilder('ad')
      .leftJoinAndSelect('ad.department', 'department')
      .leftJoinAndSelect('ad.creator', 'creator')
      .leftJoinAndSelect('ad.updater', 'updater');

    // Apply filters
    this.applyFilters(queryBuilder, filters);

    // Apply sorting
    this.applySorting(queryBuilder, filters);

    return queryBuilder;
  }

  private applyFilters(queryBuilder: SelectQueryBuilder<AdPerformance>, filters: Partial<AdPerformanceQueryDto>): void {
    const {
      search,
      platform,
      ad_type,
      status,
      objective,
      department_id,
      created_by,
      start_date_from,
      start_date_to,
      end_date_from,
      end_date_to,
      report_date_from,
      report_date_to,
      min_impressions,
      max_impressions,
      min_clicks,
      max_clicks,
      min_conversions,
      max_conversions,
      min_cost,
      max_cost,
      min_cpa,
      max_cpa,
      min_ctr,
      max_ctr,
      min_conversion_rate,
      max_conversion_rate,
      min_roas,
      max_roas,
      min_roi,
      max_roi,
      min_revenue,
      max_revenue,
      target_location,
      age_range,
      gender,
      profitable_only,
      active_only,
    } = filters;

    // Search filter
    if (search) {
      queryBuilder.andWhere(
        '(ad.campaign_name ILIKE :search OR ad.ad_set_name ILIKE :search OR ad.ad_name ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Enum filters
    if (platform) {
      queryBuilder.andWhere('ad.platform = :platform', { platform });
    }

    if (ad_type) {
      queryBuilder.andWhere('ad.ad_type = :ad_type', { ad_type });
    }

    if (status) {
      queryBuilder.andWhere('ad.status = :status', { status });
    }

    if (objective) {
      queryBuilder.andWhere('ad.objective = :objective', { objective });
    }

    // ID filters
    if (department_id) {
      queryBuilder.andWhere('ad.department_id = :department_id', { department_id });
    }

    if (created_by) {
      queryBuilder.andWhere('ad.created_by = :created_by', { created_by });
    }

    // Date filters
    if (start_date_from) {
      queryBuilder.andWhere('ad.start_date >= :start_date_from', { start_date_from });
    }

    if (start_date_to) {
      queryBuilder.andWhere('ad.start_date <= :start_date_to', { start_date_to });
    }

    if (end_date_from) {
      queryBuilder.andWhere('ad.end_date >= :end_date_from', { end_date_from });
    }

    if (end_date_to) {
      queryBuilder.andWhere('ad.end_date <= :end_date_to', { end_date_to });
    }

    if (report_date_from) {
      queryBuilder.andWhere('ad.report_date >= :report_date_from', { report_date_from });
    }

    if (report_date_to) {
      queryBuilder.andWhere('ad.report_date <= :report_date_to', { report_date_to });
    }

    // Performance filters
    if (min_impressions !== undefined) {
      queryBuilder.andWhere('ad.impressions >= :min_impressions', { min_impressions });
    }

    if (max_impressions !== undefined) {
      queryBuilder.andWhere('ad.impressions <= :max_impressions', { max_impressions });
    }

    if (min_clicks !== undefined) {
      queryBuilder.andWhere('ad.clicks >= :min_clicks', { min_clicks });
    }

    if (max_clicks !== undefined) {
      queryBuilder.andWhere('ad.clicks <= :max_clicks', { max_clicks });
    }

    if (min_conversions !== undefined) {
      queryBuilder.andWhere('ad.conversions >= :min_conversions', { min_conversions });
    }

    if (max_conversions !== undefined) {
      queryBuilder.andWhere('ad.conversions <= :max_conversions', { max_conversions });
    }

    // Cost filters
    if (min_cost !== undefined) {
      queryBuilder.andWhere('ad.total_cost >= :min_cost', { min_cost });
    }

    if (max_cost !== undefined) {
      queryBuilder.andWhere('ad.total_cost <= :max_cost', { max_cost });
    }

    if (min_cpa !== undefined) {
      queryBuilder.andWhere('ad.cost_per_acquisition >= :min_cpa', { min_cpa });
    }

    if (max_cpa !== undefined) {
      queryBuilder.andWhere('ad.cost_per_acquisition <= :max_cpa', { max_cpa });
    }

    // Performance ratio filters
    if (min_ctr !== undefined) {
      queryBuilder.andWhere('ad.click_through_rate >= :min_ctr', { min_ctr });
    }

    if (max_ctr !== undefined) {
      queryBuilder.andWhere('ad.click_through_rate <= :max_ctr', { max_ctr });
    }

    if (min_conversion_rate !== undefined) {
      queryBuilder.andWhere('ad.conversion_rate >= :min_conversion_rate', { min_conversion_rate });
    }

    if (max_conversion_rate !== undefined) {
      queryBuilder.andWhere('ad.conversion_rate <= :max_conversion_rate', { max_conversion_rate });
    }

    if (min_roas !== undefined) {
      queryBuilder.andWhere('ad.return_on_ad_spend >= :min_roas', { min_roas });
    }

    if (max_roas !== undefined) {
      queryBuilder.andWhere('ad.return_on_ad_spend <= :max_roas', { max_roas });
    }

    if (min_roi !== undefined) {
      queryBuilder.andWhere('ad.return_on_investment >= :min_roi', { min_roi });
    }

    if (max_roi !== undefined) {
      queryBuilder.andWhere('ad.return_on_investment <= :max_roi', { max_roi });
    }

    // Revenue filters
    if (min_revenue !== undefined) {
      queryBuilder.andWhere('ad.total_revenue >= :min_revenue', { min_revenue });
    }

    if (max_revenue !== undefined) {
      queryBuilder.andWhere('ad.total_revenue <= :max_revenue', { max_revenue });
    }

    // Additional filters
    if (target_location) {
      queryBuilder.andWhere('ad.target_location ILIKE :target_location', { target_location: `%${target_location}%` });
    }

    if (age_range) {
      queryBuilder.andWhere('ad.age_range = :age_range', { age_range });
    }

    if (gender) {
      queryBuilder.andWhere('ad.gender = :gender', { gender });
    }

    if (profitable_only) {
      queryBuilder.andWhere('ad.return_on_investment > 0');
    }

    if (active_only) {
      queryBuilder.andWhere('ad.status = :active_status', { active_status: AdStatus.ACTIVE });
    }
  }

  private applySorting(queryBuilder: SelectQueryBuilder<AdPerformance>, filters: Partial<AdPerformanceQueryDto>): void {
    const { sort_by = 'created_at', sort_order = 'DESC' } = filters;

    const validSortFields = [
      'campaign_name',
      'platform',
      'status',
      'start_date',
      'end_date',
      'impressions',
      'clicks',
      'conversions',
      'total_cost',
      'total_revenue',
      'click_through_rate',
      'conversion_rate',
      'cost_per_acquisition',
      'return_on_ad_spend',
      'return_on_investment',
      'created_at',
      'updated_at'
    ];

    if (validSortFields.includes(sort_by)) {
      queryBuilder.orderBy(`ad.${sort_by}`, sort_order);
    } else {
      queryBuilder.orderBy('ad.created_at', 'DESC');
    }
  }

  private applyReportFilters(queryBuilder: SelectQueryBuilder<AdPerformance>, filters: AdPerformanceReportQueryDto): void {
    const { start_date, end_date, department_id, platform, ad_type, status, objective } = filters;

    if (start_date) {
      queryBuilder.andWhere('ad.report_date >= :start_date', { start_date });
    }

    if (end_date) {
      queryBuilder.andWhere('ad.report_date <= :end_date', { end_date });
    }

    if (department_id) {
      queryBuilder.andWhere('ad.department_id = :department_id', { department_id });
    }

    if (platform) {
      queryBuilder.andWhere('ad.platform = :platform', { platform });
    }

    if (ad_type) {
      queryBuilder.andWhere('ad.ad_type = :ad_type', { ad_type });
    }

    if (status) {
      queryBuilder.andWhere('ad.status = :status', { status });
    }

    if (objective) {
      queryBuilder.andWhere('ad.objective = :objective', { objective });
    }
  }

  private async calculateStats(queryBuilder: SelectQueryBuilder<AdPerformance>): Promise<AdPerformanceStatsDto> {
    const result = await queryBuilder
      .select([
        'COUNT(ad.id) as total_campaigns',
        'COUNT(CASE WHEN ad.status = :active_status THEN 1 END) as active_campaigns',
        'SUM(ad.impressions) as total_impressions',
        'SUM(ad.clicks) as total_clicks',
        'SUM(ad.conversions) as total_conversions',
        'SUM(ad.total_cost) as total_cost',
        'SUM(ad.total_revenue) as total_revenue',
        'AVG(ad.click_through_rate) as avg_ctr',
        'AVG(ad.conversion_rate) as avg_conversion_rate',
        'AVG(ad.cost_per_acquisition) as avg_cpa',
        'AVG(ad.return_on_ad_spend) as avg_roas',
        'AVG(ad.return_on_investment) as avg_roi',
        'COUNT(CASE WHEN ad.return_on_investment > 0 THEN 1 END) * 100.0 / COUNT(ad.id) as profitable_campaigns_percentage'
      ])
      .setParameter('active_status', AdStatus.ACTIVE)
      .getRawOne();

    return {
      total_campaigns: parseInt(result.total_campaigns) || 0,
      active_campaigns: parseInt(result.active_campaigns) || 0,
      total_impressions: parseInt(result.total_impressions) || 0,
      total_clicks: parseInt(result.total_clicks) || 0,
      total_conversions: parseInt(result.total_conversions) || 0,
      total_cost: parseFloat(result.total_cost) || 0,
      total_revenue: parseFloat(result.total_revenue) || 0,
      avg_ctr: parseFloat(result.avg_ctr) || 0,
      avg_conversion_rate: parseFloat(result.avg_conversion_rate) || 0,
      avg_cpa: parseFloat(result.avg_cpa) || 0,
      avg_roas: parseFloat(result.avg_roas) || 0,
      avg_roi: parseFloat(result.avg_roi) || 0,
      profitable_campaigns_percentage: parseFloat(result.profitable_campaigns_percentage) || 0,
    };
  }

  private async getTimeSeries(
    queryBuilder: SelectQueryBuilder<AdPerformance>,
    timePeriod: string
  ): Promise<AdPerformanceTimeSeriesDto[]> {
    const formatMap = {
      daily: 'YYYY-MM-DD',
      weekly: 'YYYY-WW',
      monthly: 'YYYY-MM',
      quarterly: 'YYYY-Q',
      yearly: 'YYYY'
    };

    const format = formatMap[timePeriod] || 'YYYY-MM-DD';

    const result = await queryBuilder
      .select([
        `TO_CHAR(ad.report_date, '${format}') as date`,
        'SUM(ad.impressions) as impressions',
        'SUM(ad.clicks) as clicks',
        'SUM(ad.conversions) as conversions',
        'SUM(ad.total_cost) as cost',
        'SUM(ad.total_revenue) as revenue',
        'AVG(ad.click_through_rate) as ctr',
        'AVG(ad.conversion_rate) as conversion_rate',
        'AVG(ad.cost_per_acquisition) as cpa',
        'AVG(ad.return_on_ad_spend) as roas',
        'AVG(ad.return_on_investment) as roi'
      ])
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany();

    return result.map(item => ({
      date: item.date,
      impressions: parseInt(item.impressions) || 0,
      clicks: parseInt(item.clicks) || 0,
      conversions: parseInt(item.conversions) || 0,
      cost: parseFloat(item.cost) || 0,
      revenue: parseFloat(item.revenue) || 0,
      ctr: parseFloat(item.ctr) || 0,
      conversion_rate: parseFloat(item.conversion_rate) || 0,
      cpa: parseFloat(item.cpa) || 0,
      roas: parseFloat(item.roas) || 0,
      roi: parseFloat(item.roi) || 0,
    }));
  }

  private async getPlatformAnalysis(queryBuilder: SelectQueryBuilder<AdPerformance>): Promise<AdPerformancePlatformAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'ad.platform',
        'COUNT(ad.id) as campaigns_count',
        'SUM(ad.total_cost) as total_cost',
        'SUM(ad.total_revenue) as total_revenue',
        'SUM(ad.impressions) as total_impressions',
        'SUM(ad.clicks) as total_clicks',
        'SUM(ad.conversions) as total_conversions',
        'AVG(ad.click_through_rate) as avg_ctr',
        'AVG(ad.conversion_rate) as avg_conversion_rate',
        'AVG(ad.cost_per_acquisition) as avg_cpa',
        'AVG(ad.return_on_ad_spend) as avg_roas',
        'AVG(ad.return_on_investment) as avg_roi'
      ])
      .groupBy('ad.platform')
      .orderBy('total_cost', 'DESC')
      .getRawMany();

    // Calculate totals for percentages
    const totalCost = result.reduce((sum, item) => sum + parseFloat(item.total_cost || 0), 0);
    const totalRevenue = result.reduce((sum, item) => sum + parseFloat(item.total_revenue || 0), 0);

    return result.map(item => ({
      platform: item.platform,
      platform_name: this.getPlatformName(item.platform),
      campaigns_count: parseInt(item.campaigns_count) || 0,
      total_cost: parseFloat(item.total_cost) || 0,
      total_revenue: parseFloat(item.total_revenue) || 0,
      total_impressions: parseInt(item.total_impressions) || 0,
      total_clicks: parseInt(item.total_clicks) || 0,
      total_conversions: parseInt(item.total_conversions) || 0,
      avg_ctr: parseFloat(item.avg_ctr) || 0,
      avg_conversion_rate: parseFloat(item.avg_conversion_rate) || 0,
      avg_cpa: parseFloat(item.avg_cpa) || 0,
      avg_roas: parseFloat(item.avg_roas) || 0,
      avg_roi: parseFloat(item.avg_roi) || 0,
      cost_percentage: totalCost > 0 ? (parseFloat(item.total_cost || 0) / totalCost) * 100 : 0,
      revenue_percentage: totalRevenue > 0 ? (parseFloat(item.total_revenue || 0) / totalRevenue) * 100 : 0,
      effectiveness_score: this.calculateEffectivenessScore(
        parseFloat(item.avg_ctr) || 0,
        parseFloat(item.avg_conversion_rate) || 0,
        parseFloat(item.avg_roi) || 0
      ),
    }));
  }

  private async getDepartmentAnalysis(queryBuilder: SelectQueryBuilder<AdPerformance>): Promise<AdPerformanceDepartmentAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'ad.department_id',
        'department.name as department_name',
        'COUNT(ad.id) as campaigns_count',
        'SUM(ad.total_cost) as total_cost',
        'SUM(ad.total_revenue) as total_revenue',
        'SUM(ad.impressions) as total_impressions',
        'SUM(ad.clicks) as total_clicks',
        'SUM(ad.conversions) as total_conversions',
        'AVG(ad.click_through_rate) as avg_ctr',
        'AVG(ad.conversion_rate) as avg_conversion_rate',
        'AVG(ad.cost_per_acquisition) as avg_cpa',
        'AVG(ad.return_on_ad_spend) as avg_roas',
        'AVG(ad.return_on_investment) as avg_roi'
      ])
      .groupBy('ad.department_id, department.name')
      .orderBy('total_cost', 'DESC')
      .getRawMany();

    // Calculate totals for percentages
    const totalCost = result.reduce((sum, item) => sum + parseFloat(item.total_cost || 0), 0);
    const totalRevenue = result.reduce((sum, item) => sum + parseFloat(item.total_revenue || 0), 0);

    return result.map(item => ({
      department_id: item.department_id,
      department_name: item.department_name || 'Unknown',
      campaigns_count: parseInt(item.campaigns_count) || 0,
      total_cost: parseFloat(item.total_cost) || 0,
      total_revenue: parseFloat(item.total_revenue) || 0,
      total_impressions: parseInt(item.total_impressions) || 0,
      total_clicks: parseInt(item.total_clicks) || 0,
      total_conversions: parseInt(item.total_conversions) || 0,
      avg_ctr: parseFloat(item.avg_ctr) || 0,
      avg_conversion_rate: parseFloat(item.avg_conversion_rate) || 0,
      avg_cpa: parseFloat(item.avg_cpa) || 0,
      avg_roas: parseFloat(item.avg_roas) || 0,
      avg_roi: parseFloat(item.avg_roi) || 0,
      cost_percentage: totalCost > 0 ? (parseFloat(item.total_cost || 0) / totalCost) * 100 : 0,
      revenue_percentage: totalRevenue > 0 ? (parseFloat(item.total_revenue || 0) / totalRevenue) * 100 : 0,
      effectiveness_score: this.calculateEffectivenessScore(
        parseFloat(item.avg_ctr) || 0,
        parseFloat(item.avg_conversion_rate) || 0,
        parseFloat(item.avg_roi) || 0
      ),
    }));
  }

  private async getTopPerformers(queryBuilder: SelectQueryBuilder<AdPerformance>, limit: number): Promise<AdPerformanceTopPerformerDto[]> {
    const result = await queryBuilder
      .select([
        'ad.id',
        'ad.campaign_name',
        'ad.platform',
        'ad.total_cost',
        'ad.total_revenue',
        'ad.return_on_investment as roi',
        'ad.return_on_ad_spend as roas',
        'ad.click_through_rate as ctr',
        'ad.conversion_rate',
        'ad.cost_per_acquisition as cpa',
        'department.name as department_name'
      ])
      .orderBy('ad.return_on_investment', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map(item => ({
      id: item.id,
      campaign_name: item.campaign_name,
      platform: item.platform,
      total_cost: parseFloat(item.total_cost) || 0,
      total_revenue: parseFloat(item.total_revenue) || 0,
      roi: parseFloat(item.roi) || 0,
      roas: parseFloat(item.roas) || 0,
      ctr: parseFloat(item.ctr) || 0,
      conversion_rate: parseFloat(item.conversion_rate) || 0,
      cpa: parseFloat(item.cpa) || 0,
      effectiveness_score: this.calculateEffectivenessScore(
        parseFloat(item.ctr) || 0,
        parseFloat(item.conversion_rate) || 0,
        parseFloat(item.roi) || 0
      ),
      department_name: item.department_name || 'Unknown',
    }));
  }

  private async getComparison(query: AdPerformanceReportQueryDto, currentStats: AdPerformanceStatsDto): Promise<AdPerformanceComparisonDto> {
    // Calculate previous period dates
    const startDate = new Date(query.start_date || new Date());
    const endDate = new Date(query.end_date || new Date());
    const periodDiff = endDate.getTime() - startDate.getTime();
    
    const previousStartDate = new Date(startDate.getTime() - periodDiff);
    const previousEndDate = new Date(endDate.getTime() - periodDiff);

    // Create query for previous period
    const previousQuery = { ...query };
    previousQuery.start_date = previousStartDate.toISOString().split('T')[0];
    previousQuery.end_date = previousEndDate.toISOString().split('T')[0];

    const previousQueryBuilder = this.adPerformanceRepository.createQueryBuilder('ad')
      .leftJoinAndSelect('ad.department', 'department')
      .leftJoinAndSelect('ad.creator', 'creator');

    this.applyReportFilters(previousQueryBuilder, previousQuery);

    const previousStats = await this.calculateStats(previousQueryBuilder);

    // Calculate change percentages
    const calculateChange = (current: number, previous: number): number => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      current_period: currentStats,
      previous_period: previousStats,
      cost_change_percentage: calculateChange(currentStats.total_cost, previousStats.total_cost),
      revenue_change_percentage: calculateChange(currentStats.total_revenue, previousStats.total_revenue),
      ctr_change_percentage: calculateChange(currentStats.avg_ctr, previousStats.avg_ctr),
      conversion_rate_change_percentage: calculateChange(currentStats.avg_conversion_rate, previousStats.avg_conversion_rate),
      cpa_change_percentage: calculateChange(currentStats.avg_cpa, previousStats.avg_cpa),
      roas_change_percentage: calculateChange(currentStats.avg_roas, previousStats.avg_roas),
      roi_change_percentage: calculateChange(currentStats.avg_roi, previousStats.avg_roi),
      campaigns_change_percentage: calculateChange(currentStats.total_campaigns, previousStats.total_campaigns),
    };
  }

  private transformToResponseDto(adPerformance: AdPerformance): AdPerformanceResponseDto {
    return plainToClass(AdPerformanceResponseDto, {
      ...adPerformance,
      formattedTotalCost: adPerformance.formattedTotalCost,
      formattedRevenue: adPerformance.formattedRevenue,
      formattedCPA: adPerformance.formattedCPA,
      formattedCTR: adPerformance.formattedCTR,
      formattedConversionRate: adPerformance.formattedConversionRate,
      formattedROAS: adPerformance.formattedROAS,
      formattedROI: adPerformance.formattedROI,
      isActive: adPerformance.isActive,
      isProfitable: adPerformance.isProfitable,
      campaignDuration: adPerformance.campaignDuration,
      dailyAverageCost: adPerformance.dailyAverageCost,
      dailyAverageRevenue: adPerformance.dailyAverageRevenue,
      effectivenessScore: adPerformance.effectivenessScore,
    });
  }

  private getPlatformName(platform: AdPlatform): string {
    const platformNames = {
      [AdPlatform.GOOGLE_ADS]: 'Google Ads',
      [AdPlatform.FACEBOOK_ADS]: 'Facebook Ads',
      [AdPlatform.INSTAGRAM_ADS]: 'Instagram Ads',
      [AdPlatform.TIKTOK_ADS]: 'TikTok Ads',
      [AdPlatform.YOUTUBE_ADS]: 'YouTube Ads',
      [AdPlatform.TWITTER_ADS]: 'Twitter Ads',
      [AdPlatform.LINKEDIN_ADS]: 'LinkedIn Ads',
      [AdPlatform.SNAPCHAT_ADS]: 'Snapchat Ads',
      [AdPlatform.PINTEREST_ADS]: 'Pinterest Ads',
      [AdPlatform.OTHER]: 'Other',
    };

    return platformNames[platform] || 'Unknown';
  }

  private calculateEffectivenessScore(ctr: number, conversionRate: number, roi: number): number {
    const ctrScore = Math.min(ctr * 10, 100);
    const cvrScore = Math.min(conversionRate * 5, 100);
    const roiScore = Math.min(Math.max(roi, 0), 100);
    return (ctrScore + cvrScore + roiScore) / 3;
  }

  private formatReportPeriod(startDate: string, endDate: string): string {
    if (startDate && endDate) {
      return `${startDate} to ${endDate}`;
    }
    return 'All time';
  }
} 