import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { User } from './user.entity';
import { DepositStatus } from '../common/constants';

@Entity('deposits')
export class Deposit extends BaseEntity {
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  amount: number;

  @Column({ type: 'varchar', length: 50 })
  transaction_id: string;

  @Column({ type: 'varchar', length: 100 })
  payment_method: string;

  @Column({ type: 'varchar', length: 20, default: DepositStatus.PENDING })
  status: DepositStatus;

  @Column({ type: 'timestamp', nullable: true })
  processed_at: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  external_reference: string;

  @ManyToOne(() => Department, { nullable: false })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({ type: 'int' })
  department_id: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'uuid', nullable: true })
  user_id?: string;
} 