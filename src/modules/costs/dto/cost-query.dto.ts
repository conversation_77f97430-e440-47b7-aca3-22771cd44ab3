import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsDateString, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { CostType, CostStatus } from '../../../entities/cost.entity';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export class CostQueryDto extends PaginationDto {
  @ApiProperty({ description: 'Lọc theo loại chi phí', enum: CostType, required: false })
  @IsOptional()
  @IsEnum(CostType)
  type?: CostType;

  @ApiProperty({ description: 'Lọc theo trạng thái', enum: CostStatus, required: false })
  @IsOptional()
  @IsEnum(CostStatus)
  status?: CostStatus;

  @ApiProperty({ description: 'Lọc theo bộ phận', required: false })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiProperty({ description: 'Từ ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: 'Đến ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: 'Số tiền tối thiểu', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  minAmount?: number;

  @ApiProperty({ description: 'Số tiền tối đa', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  maxAmount?: number;

  @ApiProperty({ description: 'Lọc theo nhà cung cấp', required: false })
  @IsOptional()
  @IsString()
  vendor?: string;

  @ApiProperty({ description: 'Chỉ hiển thị đã duyệt', required: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isApproved?: boolean;

  @ApiProperty({ description: 'Chỉ hiển thị quá hạn', required: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isOverdue?: boolean;

  @ApiProperty({ description: 'Chỉ hiển thị có tài liệu', required: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  hasDocuments?: boolean;

  @ApiProperty({ description: 'Người tạo', required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;
} 