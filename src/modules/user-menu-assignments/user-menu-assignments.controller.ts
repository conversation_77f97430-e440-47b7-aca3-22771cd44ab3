import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserMenuAssignmentsService } from './user-menu-assignments.service';
import {
  CreateUserMenuAssignmentDto,
  UpdateUserMenuAssignmentDto,
  UserMenuAssignmentResponseDto,
  BulkAssignMenuDto,
  UserMenuQueryDto,
  BulkDeleteDto,
  UserMenuStructureResponseDto,
  MenuListResponseDto,
} from './dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { RoleType, PermissionsType } from '@/common/constants';
import { PaginatedResult } from '@/common/dto/pagination.dto';
import { CurrentUser } from '@/common/decorators';

@ApiTags('User Menu Assignments')
@Controller('user-menu-assignments')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
export class UserMenuAssignmentsController {
  constructor(private readonly userMenuAssignmentsService: UserMenuAssignmentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create user menu assignment' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Assignment created successfully',
    type: UserMenuAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Assignment already exists',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.CREATE)
  async create(@Body() createDto: CreateUserMenuAssignmentDto, @CurrentUser() user: any): Promise<UserMenuAssignmentResponseDto> {
    return this.userMenuAssignmentsService.create(createDto, user.fullName || user.username);
  }

  @Post('bulk-assign')
  @ApiOperation({ summary: 'Bulk assign menus to user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk assignment completed successfully',
    type: [UserMenuAssignmentResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.CREATE)
  async bulkAssign(@Body() bulkAssignDto: BulkAssignMenuDto,@CurrentUser() user: any): Promise<UserMenuAssignmentResponseDto[]> {
    return this.userMenuAssignmentsService.bulkAssign(bulkAssignDto, user.fullName || user.username);
  }

  @Get()
  @ApiOperation({ summary: 'Get all user menu assignments with pagination and filters' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignments retrieved successfully',
    type: 'PaginatedResult<UserMenuAssignmentResponseDto>',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async findAll(@Query() queryDto: UserMenuQueryDto): Promise<PaginatedResult<UserMenuAssignmentResponseDto>> {
    return this.userMenuAssignmentsService.findAll(queryDto);
  }

  @Get('menus')
  @ApiOperation({ summary: 'Get all menus with submenus' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Menu list retrieved successfully',
    type: MenuListResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getMenuList(): Promise<MenuListResponseDto> {
    return await this.userMenuAssignmentsService.getMenuList();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user menu assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment retrieved successfully',
    type: UserMenuAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<UserMenuAssignmentResponseDto> {
    return this.userMenuAssignmentsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update user menu assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment updated successfully',
    type: UserMenuAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateUserMenuAssignmentDto,
  ): Promise<UserMenuAssignmentResponseDto> {
    return this.userMenuAssignmentsService.update(id, updateDto, 'system');
  }

  @Patch(':id/bulk-assign')
  @ApiOperation({ summary: 'Update user menu assignment by ID (bulk assign)' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Assignment updated successfully (bulk assign)',
    type: UserMenuAssignmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found (bulk assign)',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  async updateBulkAssign(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() bulkAssignDto: BulkAssignMenuDto,
    @CurrentUser() user: any,
  ): Promise<UserMenuAssignmentResponseDto> {
    return this.userMenuAssignmentsService.updateBulkAssign(id, bulkAssignDto, user.fullName || user.username);
  }

  @Delete('bulk')
  @ApiOperation({ summary: 'Delete multiple user menu assignments' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Assignments deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'One or more assignments not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  async deleteBulk(@Body() deleteDto: BulkDeleteDto, @CurrentUser() user: any): Promise<void> {
    await this.userMenuAssignmentsService.deleteBulk(deleteDto.ids, user.fullName || user.username);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete user menu assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Assignment deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Assignment not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any): Promise<void> {
    await this.userMenuAssignmentsService.remove(id, user.fullName || user.username);
  }

  @Get('user/:userId/menu/:menuId/permissions')
  @ApiOperation({ summary: 'Get user permissions for a specific menu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'menuId', description: 'Menu ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Menu permissions retrieved successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getUserMenuPermissions(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('menuId', ParseUUIDPipe) menuId: string,
  ) {
    return this.userMenuAssignmentsService.getUserMenuPermissions(userId, menuId);
  }



  @Get('check/user/:userId/menu/:menuId/permission/:permissionName')
  @ApiOperation({ summary: 'Check if user has specific permission for a menu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'menuId', description: 'Menu ID', type: 'string' })
  @ApiParam({ name: 'permissionName', description: 'Permission name', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission check completed',
    schema: { type: 'object', properties: { hasPermission: { type: 'boolean' } } },
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async checkUserMenuPermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('menuId', ParseUUIDPipe) menuId: string,
    @Param('permissionName') permissionName: string,
  ) {
    const hasPermission = await this.userMenuAssignmentsService.userHasMenuPermission(userId, menuId, permissionName);
    return { hasPermission };
  }

  @Get('check/user/:userId/submenu/:submenuId/permission/:permissionName')
  @ApiOperation({ summary: 'Check if user has specific permission for a submenu' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiParam({ name: 'submenuId', description: 'SubMenu ID', type: 'string' })
  @ApiParam({ name: 'permissionName', description: 'Permission name', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission check completed',
    schema: { type: 'object', properties: { hasPermission: { type: 'boolean' } } },
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async checkUserSubmenuPermission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Param('submenuId', ParseUUIDPipe) submenuId: string,
    @Param('permissionName') permissionName: string,
  ) {
    const hasPermission = await this.userMenuAssignmentsService.userHasSubmenuPermission(userId, submenuId, permissionName);
    return { hasPermission };
  }

  @Get('user/:userId/menu-structure')
  @ApiOperation({ summary: 'Get user menu structure with permissions' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User menu structure retrieved successfully',
    type: UserMenuStructureResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  async getUserMenuStructure(@Param('userId', ParseUUIDPipe) userId: string): Promise<UserMenuStructureResponseDto> {
    return await this.userMenuAssignmentsService.getUserMenuStructure(userId);
  }

}
