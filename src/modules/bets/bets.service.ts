import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Bet } from '@/entities/bet.entity';
import { Department } from '@/entities/department.entity';
import { CreateBetDto } from './dto/create-bet.dto';
import { UpdateBetDto } from './dto/update-bet.dto';
import { BetChartDto } from './dto/bet-chart.dto';
import { createPaginatedResult, PaginationDto } from '@/common/dto/pagination.dto';
import { User } from '@/entities';

@Injectable()
export class BetsService {
  constructor(
    @InjectRepository(Bet)
    private betRepository: Repository<Bet>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async create(createBetDto: CreateBetDto, user: User): Promise<Bet> {
    const departmentId = user.departmentId || null;

    const department = await this.departmentRepository.findOne({
      where: { id: departmentId ?? createBetDto.department_id }
    });
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const bet = this.betRepository.create({
      ...createBetDto,
      status: createBetDto.status as any,
      department: { id: createBetDto.department_id },
      createdBy: user.id,
      updatedBy: user.id,
    });
    return await this.betRepository.save(bet);

  }

  async findAll(paginationDto: PaginationDto): Promise<{ data: Bet[]; total: number }> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.betRepository
      .createQueryBuilder('bet')
      .leftJoinAndSelect('bet.department', 'department');

    if (search) {
      queryBuilder.where(
        'bet.bet_id LIKE :search OR bet.game_type LIKE :search OR bet.game_name LIKE :search OR bet.status LIKE :search',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('bet.createdAt', 'DESC')
      .getManyAndCount();

    return createPaginatedResult(data, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Bet> {
    const bet = await this.betRepository.findOne({
      where: { id },
      relations: ['department']
    });

    if (!bet) {
      throw new NotFoundException('Bet not found');
    }

    return bet;
  }

  async update(id: string, updateBetDto: UpdateBetDto): Promise<Bet> {
    const bet = await this.findOne(id);

    if (updateBetDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateBetDto.department_id }
      });
      if (!department) {
        throw new NotFoundException('Department not found');
      }
    }

    Object.assign(bet, updateBetDto);
    return await this.betRepository.save(bet);
  }

  async remove(id: string): Promise<void> {
    const bet = await this.findOne(id);
    await this.betRepository.remove(bet);
  }

  async importFromExcel(file: any): Promise<{ success: number; errors: string[] }> {
    // Placeholder for import functionality
    // In a real implementation, you would use a library like xlsx
    const errors: string[] = [];
    let success = 0;

    // Mock implementation
    return { success, errors };
  }

  async exportToExcel(): Promise<string> {
    const bets = await this.betRepository.find({
      relations: ['department'],
      order: { createdAt: 'DESC' }
    });

    const data = bets.map(bet => ({
      id: bet.id,
      bet_amount: bet.bet_amount,
      win_amount: bet.win_amount,
      loss_amount: bet.loss_amount,
      bet_id: bet.bet_id,
      game_type: bet.game_type,
      game_name: bet.game_name,
      status: bet.status,
      settled_at: bet.settled_at,
      notes: bet.notes,
      external_reference: bet.external_reference,
      department_id: bet.department_id,
      department_name: bet.department?.name,
      created_at: bet.createdAt,
      updated_at: bet.updatedAt,
    }));

    // Placeholder for export functionality
    // In a real implementation, you would use a library like xlsx
    const fileName = `bets_${new Date().toISOString().split('T')[0]}.xlsx`;
    return fileName;
  }

  async getChartData(chartDto: BetChartDto) {
    const { from_date, to_date, department_id, chart_type = 'daily' } = chartDto;
    
    let dateFormat: string;

    switch (chart_type) {
      case 'daily':
        dateFormat = 'DATE(created_at)';
        break;
      case 'weekly':
        dateFormat = 'YEARWEEK(created_at)';
        break;
      case 'monthly':
        dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
        break;
      default:
        dateFormat = 'DATE(created_at)';
    }

    const query = `
      SELECT 
        ${dateFormat} as date,
        SUM(bet_amount) as total_bet_amount,
        SUM(win_amount) as total_win_amount,
        SUM(loss_amount) as total_loss_amount,
        COUNT(*) as total_bets,
        SUM(CASE WHEN status = 'won' THEN 1 ELSE 0 END) as won_bets,
        SUM(CASE WHEN status = 'lost' THEN 1 ELSE 0 END) as lost_bets,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_bets
      FROM bets
      WHERE 1=1
        ${from_date ? `AND created_at >= '${from_date}'` : ''}
        ${to_date ? `AND created_at <= '${to_date}'` : ''}
        ${department_id ? `AND department_id = ${department_id}` : ''}
      GROUP BY ${dateFormat}
      ORDER BY date DESC
    `;

    const result = await this.betRepository.query(query);
    return result;
  }

  async getDepartmentStats() {
    const query = `
      SELECT 
        d.id,
        d.name as department_name,
        COUNT(b.id) as total_bets,
        SUM(b.bet_amount) as total_bet_amount,
        SUM(b.win_amount) as total_win_amount,
        SUM(b.loss_amount) as total_loss_amount,
        AVG(b.bet_amount) as avg_bet_amount,
        SUM(CASE WHEN b.status = 'won' THEN b.bet_amount ELSE 0 END) as won_amount,
        SUM(CASE WHEN b.status = 'lost' THEN b.bet_amount ELSE 0 END) as lost_amount
      FROM departments d
      LEFT JOIN bets b ON d.id = b.department_id
      GROUP BY d.id, d.name
      ORDER BY total_bet_amount DESC
    `;

    return await this.betRepository.query(query);
  }
} 