### User Menu Assignments API Tests
### Base URL and Authentication
@baseUrl = http://localhost:3000/api/v1
@token = your-jwt-token-here

### Variables - Replace with actual UUIDs from your database
@userId = 123e4567-e89b-12d3-a456-************
@menuId = 6892efc8-0764-4608-946b-60d56271837b
@submenuId = f64f580b-340c-428a-b155-ff6a3241f4c2
@permissionId1 = 10e23b0a-4e9f-4350-833b-218d04799271
@permissionId2 = a371d638-9ded-4259-b457-5f849d72f082
@departmentId = dept-uuid-here
@teamId = team-uuid-here
@roleId = role-uuid-here
@assignmentId = assignment-uuid-here

### 1. Login first to get JWT token
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your-password"
}

###

### 2. Get all assignments (to see existing data)
GET {{baseUrl}}/user-menu-assignments
Authorization: Bearer {{token}}

###

### 3. Test bulkAssign - Create bulk assignments
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "departmentId": "{{departmentId}}",
  "teamId": "{{teamId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": [
        "{{permissionId1}}",
        "{{permissionId2}}"
      ]
    }
  ],
  "subMenu": [
    {
      "id": "{{submenuId}}",
      "permissionIds": [
        "{{permissionId1}}"
      ]
    }
  ]
}

###

### 4. Test bulkAssign - Only menu (without submenu)
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": [
        "{{permissionId1}}"
      ]
    }
  ]
}

###

### 5. Test bulkAssign - Only submenu (without menu)
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "subMenu": [
    {
      "id": "{{submenuId}}",
      "permissionIds": [
        "{{permissionId1}}",
        "{{permissionId2}}"
      ]
    }
  ]
}

###

### 6. Test updateBulkAssign - Update existing assignment
PATCH {{baseUrl}}/user-menu-assignments/{{assignmentId}}/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "departmentId": "{{departmentId}}",
  "teamId": "{{teamId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": [
        "{{permissionId2}}"
      ]
    }
  ]
}

###

### 7. Test updateBulkAssign - Update with different permissions
PATCH {{baseUrl}}/user-menu-assignments/{{assignmentId}}/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "subMenu": [
    {
      "id": "{{submenuId}}",
      "permissionIds": [
        "{{permissionId1}}",
        "{{permissionId2}}"
      ]
    }
  ]
}

###

### 8. Test deleteBulk - Delete multiple assignments
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [
    "assignment-id-1",
    "assignment-id-2"
  ]
}

###

### 9. Get specific assignment by ID
GET {{baseUrl}}/user-menu-assignments/{{assignmentId}}
Authorization: Bearer {{token}}

###

### 10. Test error cases - Invalid user ID
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "invalid-uuid",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["{{permissionId1}}"]
    }
  ]
}

###

### 11. Test error cases - Non-existent user
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "00000000-0000-0000-0000-000000000000",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["{{permissionId1}}"]
    }
  ]
}

###

### 12. Test error cases - Invalid permission ID
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["invalid-permission-id"]
    }
  ]
}

###

### 13. Test error cases - updateBulkAssign with non-existent assignment ID
PATCH {{baseUrl}}/user-menu-assignments/00000000-0000-0000-0000-000000000000/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["{{permissionId1}}"]
    }
  ]
}

###

### 14. Test error cases - deleteBulk with invalid IDs
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [
    "invalid-uuid",
    "another-invalid-uuid"
  ]
}

###

### 15. Test error cases - deleteBulk with empty array
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": []
}

###

### 16. Test bulkAssign - Duplicate assignment (should fail)
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{userId}}",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["{{permissionId1}}"]
    }
  ]
}

###

### 17. Test updateBulkAssign - Change user ID
PATCH {{baseUrl}}/user-menu-assignments/{{assignmentId}}/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "different-user-id-here",
  "roleId": "{{roleId}}",
  "menu": [
    {
      "id": "{{menuId}}",
      "permissionIds": ["{{permissionId1}}"]
    }
  ]
}

###

### 18. Get user permissions for specific menu
GET {{baseUrl}}/user-menu-assignments/user/{{userId}}/menu/{{menuId}}/permissions
Authorization: Bearer {{token}}

###

### 19. Check if user has specific permission for menu
GET {{baseUrl}}/user-menu-assignments/check/user/{{userId}}/menu/{{menuId}}/permission/read
Authorization: Bearer {{token}}

###

### 20. Check if user has specific permission for submenu
GET {{baseUrl}}/user-menu-assignments/check/user/{{userId}}/submenu/{{submenuId}}/permission/read
Authorization: Bearer {{token}}
