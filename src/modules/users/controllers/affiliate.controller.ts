import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AffiliateService } from '../services/affiliate.service';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { RoleType } from '@/common/constants/role.constant';
import { PermissionsType } from '@/common/constants/permissions.constant';
import { GenerateAffiliateCodeDto, UpdateAffiliateCodeDto, ToggleAffiliateCodeDto } from '../dto/affiliate.dto';

@ApiTags('Affiliate')
@Controller('affiliate')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateController {
  constructor(private readonly affiliateService: AffiliateService) {}

  @Post('generate/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Generate affiliate code for a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Affiliate code generated successfully',
    schema: {
      type: 'object',
      properties: {
        affiliateCode: { type: 'string' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Affiliate code already exists',
  })
  async generateAffiliateCode(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() generateDto: GenerateAffiliateCodeDto,
  ): Promise<{ affiliateCode: string; message: string }> {
    const affiliateCode = await this.affiliateService.generateAffiliateCode(
      userId,
      generateDto.customCode,
    );
    return {
      affiliateCode,
      message: 'Affiliate code generated successfully',
    };
  }

  @Get('stats/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get affiliate statistics for a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalReferrals: { type: 'number' },
        activeReferrals: { type: 'number' },
        affiliateCode: { type: 'string', nullable: true },
        isActive: { type: 'boolean' },
      },
    },
  })
  async getAffiliateStats(@Param('userId', ParseUUIDPipe) userId: string) {
    return await this.affiliateService.getAffiliateStats(userId);
  }

  @Get('my-stats')
  @ApiOperation({ summary: 'Get current user affiliate statistics' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current user affiliate statistics retrieved successfully',
  })
  async getMyAffiliateStats(@CurrentUser() currentUser: any) {
    return await this.affiliateService.getAffiliateStats(currentUser.id);
  }

  @Get('referrals/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get users referred by an affiliate' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Referred users retrieved successfully',
  })
  async getReferredUsers(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.affiliateService.getReferredUsers(userId, page, limit);
  }

  @Get('my-referrals')
  @ApiOperation({ summary: 'Get users referred by current user' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current user referred users retrieved successfully',
  })
  async getMyReferrals(
    @CurrentUser() currentUser: any,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.affiliateService.getReferredUsers(currentUser.id, page, limit);
  }

  @Put('toggle/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Activate or deactivate affiliate code' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate code status updated successfully',
  })
  async toggleAffiliateCode(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() toggleDto: ToggleAffiliateCodeDto,
  ): Promise<{ message: string }> {
    await this.affiliateService.toggleAffiliateCode(userId, toggleDto.active);
    return {
      message: `Affiliate code ${toggleDto.active ? 'activated' : 'deactivated'} successfully`,
    };
  }

  @Put('update/:userId')
  @UseGuards(PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Update affiliate code' })
  @ApiParam({ name: 'userId', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Affiliate code updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Affiliate code already exists',
  })
  async updateAffiliateCode(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() updateDto: UpdateAffiliateCodeDto,
  ): Promise<{ message: string }> {
    await this.affiliateService.updateAffiliateCode(userId, updateDto.newCode);
    return {
      message: 'Affiliate code updated successfully',
    };
  }
}
