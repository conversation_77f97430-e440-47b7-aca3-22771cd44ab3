### Setup Test Data - Get actual UUIDs from database
### Run these requests first to get real UUIDs for testing

@baseUrl = http://localhost:3000/api/v1
@token = your-jwt-token-here

### 1. Login to get JWT token
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your-password"
}

###

### 2. Get all users (to get userId)
GET {{baseUrl}}/users?limit=5
Authorization: Bearer {{token}}

###

### 3. Get all menus (to get menuId)
GET {{baseUrl}}/menus?limit=5
Authorization: Bearer {{token}}

###

### 4. Get all submenus (to get submenuId)
GET {{baseUrl}}/submenus?limit=5
Authorization: Bearer {{token}}

###

### 5. Get all permissions (to get permissionIds)
GET {{baseUrl}}/permissions?limit=10
Authorization: Bearer {{token}}

###

### 6. Get all departments (to get departmentId)
GET {{baseUrl}}/departments?limit=5
Authorization: Bearer {{token}}

###

### 7. Get all teams (to get teamId)
GET {{baseUrl}}/teams?limit=5
Authorization: Bearer {{token}}

###

### 8. Get all roles (to get roleId)
GET {{baseUrl}}/roles?limit=5
Authorization: Bearer {{token}}

###

### 9. Get existing user menu assignments (to get assignmentId for update tests)
GET {{baseUrl}}/user-menu-assignments?limit=5
Authorization: Bearer {{token}}

###

### 10. Create a test user (if needed)
POST {{baseUrl}}/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "fullName": "Test User",
  "password": "testpassword123",
  "isActive": true
}

###

### 11. Create a test menu (if needed)
POST {{baseUrl}}/menus
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Test Menu",
  "description": "Menu for testing",
  "icon": "test-icon",
  "order": 1,
  "isActive": true
}

###

### 12. Create a test submenu (if needed)
POST {{baseUrl}}/submenus
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Test SubMenu",
  "description": "SubMenu for testing",
  "link": "/test-submenu",
  "icon": "test-icon",
  "order": 1,
  "menuId": "your-menu-id-here",
  "isActive": true
}

###

### 13. Create test permissions (if needed)
POST {{baseUrl}}/permissions
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_READ",
  "action": "read",
  "description": "Test read permission"
}

###

POST {{baseUrl}}/permissions
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_WRITE",
  "action": "write", 
  "description": "Test write permission"
}

###

### 14. Create test department (if needed)
POST {{baseUrl}}/departments
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Test Department",
  "description": "Department for testing",
  "isActive": true
}

###

### 15. Create test team (if needed)
POST {{baseUrl}}/teams
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Test Team",
  "description": "Team for testing",
  "departmentId": "your-department-id-here",
  "isActive": true
}

###

### 16. Create test role (if needed)
POST {{baseUrl}}/roles
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_ROLE",
  "description": "Role for testing",
  "isActive": true
}
