import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CostsService } from './costs.service';
import { CreateCostDto } from './dto/create-cost.dto';
import { UpdateCostDto } from './dto/update-cost.dto';
import { CostQueryDto } from './dto/cost-query.dto';
import { CostResponseDto } from './dto/cost-response.dto';
import { 
  CostReportQueryDto, 
  CostReportResponseDto, 
  ApproveCostDto 
} from './dto/cost-report.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { PermissionsType, RoleType } from '@/common/constants';
import { CurrentUser } from '@/common/decorators';
import { User } from '@/entities';
import { PaginatedResult } from '@/common/dto/pagination.dto';

@ApiTags('costs')
@Controller('costs')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class CostsController {
  constructor(private readonly costsService: CostsService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo chi phí mới' })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Chi phí đã được tạo thành công', 
    type: CostResponseDto 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.CREATE)
  create(@Body() createCostDto: CreateCostDto, @CurrentUser() user: User) {
    return this.costsService.create(createCostDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách chi phí' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Danh sách chi phí', 
    type: CostResponseDto,
    isArray: true 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findAll(@Query() queryDto: CostQueryDto): Promise<PaginatedResult<CostResponseDto>> {
    return this.costsService.findAll(queryDto);
  }

  @Get('report')
  @ApiOperation({ summary: 'Lấy báo cáo chi phí theo bộ phận' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo chi phí theo bộ phận', 
    type: CostReportResponseDto 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  getCostReport(@Query() queryDto: CostReportQueryDto): Promise<CostReportResponseDto> {
    return this.costsService.getCostReport(queryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi phí theo ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chi phí theo ID', 
    type: CostResponseDto 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy chi phí' 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findOne(@Param('id') id: string): Promise<CostResponseDto> {
    return this.costsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật chi phí' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chi phí đã được cập nhật thành công', 
    type: CostResponseDto 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy chi phí' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền cập nhật chi phí này' 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.UPDATE)
  update(
    @Param('id') id: string, 
    @Body() updateCostDto: UpdateCostDto, 
    @CurrentUser() user: User
  ): Promise<CostResponseDto> {
    return this.costsService.update(id, updateCostDto, user);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa chi phí' })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'Chi phí đã được xóa thành công' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy chi phí' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền xóa chi phí này' 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.DELETE)
  remove(@Param('id') id: string, @CurrentUser() user: User): Promise<void> {
    return this.costsService.remove(id, user);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'Duyệt/Từ chối chi phí' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chi phí đã được duyệt/từ chối thành công', 
    type: CostResponseDto 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy chi phí' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền duyệt chi phí' 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.APPROVE)
  approveCost(
    @Param('id') id: string, 
    @Body() approveDto: ApproveCostDto, 
    @CurrentUser() user: User
  ): Promise<CostResponseDto> {
    return this.costsService.approveCost(id, approveDto, user);
  }
} 