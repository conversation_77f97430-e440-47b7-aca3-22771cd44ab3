import { <PERSON><PERSON><PERSON>, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';

/**
 * Affiliate Entity
 * Represents affiliate codes and their associated information
 */
@Entity('affiliates')
export class Affiliate extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: 'Unique affiliate code',
  })
  code: string;

  @Column({
    type: 'uuid',
    comment: 'User ID who owns this affiliate code',
  })
  userId: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Description or name for the affiliate code',
  })
  description?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the affiliate code is active',
  })
  isActive: boolean;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Date when the affiliate code was activated',
  })
  activatedAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Date when the affiliate code was deactivated',
  })
  deactivatedAt?: Date;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Total number of times this affiliate code has been used',
  })
  totalUsageCount: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of successful referrals from this affiliate code',
  })
  successfulReferrals: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    comment: 'Commission rate for this affiliate (percentage)',
  })
  commissionRate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total commission earned from this affiliate code',
  })
  totalCommissionEarned: number;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Date when the affiliate code expires',
  })
  expiresAt?: Date;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Maximum number of uses allowed for this affiliate code',
  })
  maxUsageLimit?: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'general',
    comment: 'Category or type of affiliate code',
  })
  category: string;

  // Relationships
  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'userId' })
  user?: User;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get isUsageLimitReached(): boolean {
    return this.maxUsageLimit ? this.totalUsageCount >= this.maxUsageLimit : false;
  }

  get canBeUsed(): boolean {
    return this.isActive && !this.isExpired && !this.isUsageLimitReached;
  }

  get conversionRate(): number {
    return this.totalUsageCount > 0 ? (this.successfulReferrals / this.totalUsageCount) * 100 : 0;
  }
}
