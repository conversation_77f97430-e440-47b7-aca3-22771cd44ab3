import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Bet } from '@/entities/bet.entity';
import { Department } from '@/entities/department.entity';
import { BetsController } from './bets.controller';
import { BetsService } from './bets.service';

@Module({
  imports: [TypeOrmModule.forFeature([Bet, Department])],
  controllers: [BetsController],
  providers: [BetsService],
  exports: [BetsService],
})
export class BetsModule {} 