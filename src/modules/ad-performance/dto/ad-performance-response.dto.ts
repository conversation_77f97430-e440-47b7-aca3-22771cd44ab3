import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { AdPlatform, AdType, AdStatus, AdObjective } from '../../../entities/ad-performance.entity';

export class AdPerformanceDepartmentDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;
}

export class AdPerformanceUserDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  username: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  full_name: string;
}

export class AdPerformanceResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ description: 'Tên chiến dịch quảng cáo' })
  campaign_name: string;

  @ApiPropertyOptional({ description: 'Tên ad set' })
  ad_set_name?: string;

  @ApiPropertyOptional({ description: 'Tên quảng cáo' })
  ad_name?: string;

  @ApiProperty({ enum: AdPlatform, description: 'Nền tảng quảng cáo' })
  platform: AdPlatform;

  @ApiProperty({ enum: AdType, description: 'Loại quảng cáo' })
  ad_type: AdType;

  @ApiProperty({ enum: AdStatus, description: 'Trạng thái quảng cáo' })
  status: AdStatus;

  @ApiProperty({ enum: AdObjective, description: 'Mục tiêu quảng cáo' })
  objective: AdObjective;

  // Performance Metrics
  @ApiProperty({ description: 'Số lần hiển thị' })
  impressions: number;

  @ApiProperty({ description: 'Số lần click' })
  clicks: number;

  @ApiProperty({ description: 'Số lần chuyển đổi' })
  conversions: number;

  @ApiProperty({ description: 'Số leads' })
  leads: number;

  @ApiProperty({ description: 'Số đăng ký' })
  registrations: number;

  @ApiProperty({ description: 'Số deposits' })
  deposits: number;

  @ApiProperty({ description: 'Số first time deposits' })
  first_time_deposits: number;

  // Cost Metrics
  @ApiProperty({ description: 'Tổng chi phí' })
  total_cost: number;

  @ApiProperty({ description: 'Chi phí mỗi click' })
  cost_per_click: number;

  @ApiProperty({ description: 'Chi phí mỗi nghìn lần hiển thị (CPM)' })
  cost_per_mille: number;

  @ApiProperty({ description: 'Chi phí mỗi chuyển đổi (CPA)' })
  cost_per_acquisition: number;

  @ApiProperty({ description: 'Chi phí mỗi lead' })
  cost_per_lead: number;

  @ApiProperty({ description: 'Chi phí mỗi đăng ký' })
  cost_per_registration: number;

  @ApiProperty({ description: 'Chi phí mỗi deposit' })
  cost_per_deposit: number;

  @ApiProperty({ description: 'Chi phí mỗi FTD' })
  cost_per_ftd: number;

  // Revenue Metrics
  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'Tổng tiền deposit' })
  deposit_amount: number;

  @ApiProperty({ description: 'Tổng tiền FTD' })
  ftd_amount: number;

  // Calculated Performance Ratios
  @ApiProperty({ description: 'Tỷ lệ click qua (CTR)' })
  click_through_rate: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi (CVR)' })
  conversion_rate: number;

  @ApiProperty({ description: 'Tỷ lệ đăng ký' })
  registration_rate: number;

  @ApiProperty({ description: 'Tỷ lệ deposit' })
  deposit_rate: number;

  @ApiProperty({ description: 'Tỷ lệ FTD' })
  ftd_rate: number;

  @ApiProperty({ description: 'Tỷ suất sinh lời trên chi phí quảng cáo (ROAS)' })
  return_on_ad_spend: number;

  @ApiProperty({ description: 'Tỷ suất sinh lời đầu tư (ROI)' })
  return_on_investment: number;

  // Time Period
  @ApiProperty({ description: 'Ngày bắt đầu chiến dịch' })
  start_date: Date;

  @ApiProperty({ description: 'Ngày kết thúc chiến dịch' })
  end_date: Date;

  @ApiProperty({ description: 'Ngày báo cáo' })
  report_date: Date;

  // Targeting Information
  @ApiPropertyOptional({ description: 'Đối tượng mục tiêu' })
  target_audience?: string;

  @ApiPropertyOptional({ description: 'Vị trí mục tiêu' })
  target_location?: string;

  @ApiPropertyOptional({ description: 'Độ tuổi mục tiêu' })
  age_range?: string;

  @ApiPropertyOptional({ description: 'Giới tính mục tiêu' })
  gender?: string;

  @ApiPropertyOptional({ description: 'Sở thích mục tiêu' })
  interests?: string;

  @ApiPropertyOptional({ description: 'Từ khóa mục tiêu' })
  keywords?: string;

  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  currency: string;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  notes?: string;

  @ApiPropertyOptional({ description: 'URL chiến dịch' })
  campaign_url?: string;

  @ApiPropertyOptional({ description: 'URL landing page' })
  landing_page_url?: string;

  // Relationships
  @ApiPropertyOptional({ type: AdPerformanceDepartmentDto, description: 'Thông tin phòng ban' })
  @Type(() => AdPerformanceDepartmentDto)
  department?: AdPerformanceDepartmentDto;

  @ApiPropertyOptional({ type: AdPerformanceUserDto, description: 'Người tạo' })
  @Type(() => AdPerformanceUserDto)
  creator?: AdPerformanceUserDto;

  @ApiPropertyOptional({ type: AdPerformanceUserDto, description: 'Người cập nhật' })
  @Type(() => AdPerformanceUserDto)
  updater?: AdPerformanceUserDto;

  @ApiProperty({ description: 'Ngày tạo' })
  created_at: Date;

  @ApiProperty({ description: 'Ngày cập nhật' })
  updated_at: Date;

  // Computed Properties
  @ApiProperty({ description: 'Tổng chi phí đã format' })
  formattedTotalCost: string;

  @ApiProperty({ description: 'Tổng doanh thu đã format' })
  formattedRevenue: string;

  @ApiProperty({ description: 'CPA đã format' })
  formattedCPA: string;

  @ApiProperty({ description: 'CTR đã format' })
  formattedCTR: string;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi đã format' })
  formattedConversionRate: string;

  @ApiProperty({ description: 'ROAS đã format' })
  formattedROAS: string;

  @ApiProperty({ description: 'ROI đã format' })
  formattedROI: string;

  @ApiProperty({ description: 'Có đang hoạt động không' })
  isActive: boolean;

  @ApiProperty({ description: 'Có sinh lời không' })
  isProfitable: boolean;

  @ApiProperty({ description: 'Số ngày chạy chiến dịch' })
  campaignDuration: number;

  @ApiProperty({ description: 'Chi phí trung bình hàng ngày' })
  dailyAverageCost: number;

  @ApiProperty({ description: 'Doanh thu trung bình hàng ngày' })
  dailyAverageRevenue: number;

  @ApiProperty({ description: 'Điểm hiệu quả' })
  effectivenessScore: number;
} 