import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Deposit, Bet, User } from '@/entities';
import { 
  DashboardChartDto, 
  FTDChartResponseDto, 
  TotalBetsChartResponseDto,
  DepositorsChartResponseDto,
  RevenueChartResponseDto,
  ChartType
} from './dto/dashboard-chart.dto';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Deposit)
    private readonly depositRepository: Repository<Deposit>,
    @InjectRepository(Bet)
    private readonly betRepository: Repository<Bet>,
  ) {}

  /**
   * Get FTD (First Time Deposit) chart data
   */
  async getFTDChartData(chartDto: DashboardChartDto): Promise<FTDChartResponseDto[]> {
    const { from_date, to_date, department_id, chart_type = ChartType.DAILY } = chartDto;
    
    const dateFormat = this.getDateFormat(chart_type);
    
    // Get total unique users for FTD rate calculation
    const totalUsersQuery = this.depositRepository
      .createQueryBuilder('d_total')
      .select('COUNT(DISTINCT d_total.user_id)', 'total_users');
    
    if (from_date) {
      totalUsersQuery.andWhere('d_total.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      totalUsersQuery.andWhere('d_total.created_at <= :to_date', { to_date });
    }

    const totalUsersResult = await totalUsersQuery.getRawOne();
    const totalUsers = parseInt(totalUsersResult.total_users) || 1;

    // Main query for FTD data with subquery for first deposits
    const queryBuilder = this.depositRepository
      .createQueryBuilder('d')
      .select([
        `${dateFormat} as date`,
        'COUNT(DISTINCT d.user_id) as ftd_count',
        'SUM(d.amount) as ftd_amount',
        `ROUND((COUNT(DISTINCT d.user_id) * 100.0 / ${totalUsers}), 2) as ftd_rate`
      ])
      .where((qb) => {
        const subQuery = qb.subQuery()
          .select('MIN(d_sub.id)')
          .from(Deposit, 'd_sub')
          .where('d_sub.user_id = d.user_id');
        
        if (department_id) {
          subQuery.andWhere('d_sub.department_id = :department_id');
        }
        if (from_date) {
          subQuery.andWhere('d_sub.created_at >= :from_date');
        }
        if (to_date) {
          subQuery.andWhere('d_sub.created_at <= :to_date');
        }
        
        return 'd.id IN ' + subQuery.getQuery();
      });

    // Apply date filters to main query
    if (from_date) {
      queryBuilder.andWhere('d.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      queryBuilder.andWhere('d.created_at <= :to_date', { to_date });
    }
    if (department_id) {
      queryBuilder.andWhere('d.department_id = :department_id', { department_id });
    }

    queryBuilder
      .groupBy(`${dateFormat}`)
      .orderBy('date', 'DESC');

    const result = await queryBuilder.getRawMany();
    
    return result.map(row => ({
      date: row.date,
      ftd_count: parseInt(row.ftd_count) || 0,
      ftd_amount: parseFloat(row.ftd_amount) || 0,
      ftd_rate: parseFloat(row.ftd_rate) || 0,
    }));
  }

  /**
   * Get Total Bets chart data
   */
  async getTotalBetsChartData(chartDto: DashboardChartDto): Promise<TotalBetsChartResponseDto[]> {
    const { from_date, to_date, department_id, chart_type = ChartType.DAILY } = chartDto;
    
    const dateFormat = this.getDateFormat(chart_type);
    
    const queryBuilder = this.betRepository
      .createQueryBuilder('b')
      .select([
        `${dateFormat} as date`,
        'COUNT(*) as total_bets',
        'SUM(b.bet_amount) as total_bet_amount',
        'SUM(b.win_amount) as total_win_amount',
        'SUM(b.loss_amount) as total_loss_amount',
        `ROUND((SUM(CASE WHEN b.status = 'won' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) as win_rate`
      ]);

    // Apply filters
    if (from_date) {
      queryBuilder.andWhere('b.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      queryBuilder.andWhere('b.created_at <= :to_date', { to_date });
    }
    if (department_id) {
      queryBuilder.andWhere('b.department_id = :department_id', { department_id });
    }

    queryBuilder
      .groupBy(`${dateFormat}`)
      .orderBy('date', 'DESC');

    const result = await queryBuilder.getRawMany();
    
    return result.map(row => ({
      date: row.date,
      total_bets: parseInt(row.total_bets) || 0,
      total_bet_amount: parseFloat(row.total_bet_amount) || 0,
      total_win_amount: parseFloat(row.total_win_amount) || 0,
      total_loss_amount: parseFloat(row.total_loss_amount) || 0,
      win_rate: parseFloat(row.win_rate) || 0,
    }));
  }

  /**
   * Get Depositors chart data
   */
  async getDepositorsChartData(chartDto: DashboardChartDto): Promise<DepositorsChartResponseDto[]> {
    const { from_date, to_date, department_id, chart_type = ChartType.DAILY } = chartDto;
    
    const dateFormat = this.getDateFormat(chart_type);
    
    const queryBuilder = this.depositRepository
      .createQueryBuilder('d')
      .select([
        `${dateFormat} as date`,
        'COUNT(DISTINCT d.user_id) as depositors_count',
        'SUM(d.amount) as total_deposit_amount',
        'COUNT(*) as deposit_transactions',
        'ROUND(AVG(d.amount), 2) as avg_deposit_amount'
      ])
      .where("d.status = 'completed'");

    // Apply filters
    if (from_date) {
      queryBuilder.andWhere('d.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      queryBuilder.andWhere('d.created_at <= :to_date', { to_date });
    }
    if (department_id) {
      queryBuilder.andWhere('d.department_id = :department_id', { department_id });
    }

    queryBuilder
      .groupBy(`${dateFormat}`)
      .orderBy('date', 'DESC');

    const result = await queryBuilder.getRawMany();
    
    return result.map(row => ({
      date: row.date,
      depositors_count: parseInt(row.depositors_count) || 0,
      total_deposit_amount: parseFloat(row.total_deposit_amount) || 0,
      deposit_transactions: parseInt(row.deposit_transactions) || 0,
      avg_deposit_amount: parseFloat(row.avg_deposit_amount) || 0,
    }));
  }

  /**
   * Get Revenue chart data
   */
  async getRevenueChartData(chartDto: DashboardChartDto): Promise<RevenueChartResponseDto[]> {
    const { from_date, to_date, department_id, chart_type = ChartType.DAILY } = chartDto;
    
    const dateFormat = this.getDateFormat(chart_type);
    
    // Get deposit revenue
    const depositQueryBuilder = this.depositRepository
      .createQueryBuilder('d')
      .select([
        `${dateFormat} as date`,
        'SUM(d.amount) as deposit_revenue'
      ])
      .where("d.status = 'completed'");

    // Apply filters for deposit query
    if (from_date) {
      depositQueryBuilder.andWhere('d.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      depositQueryBuilder.andWhere('d.created_at <= :to_date', { to_date });
    }
    if (department_id) {
      depositQueryBuilder.andWhere('d.department_id = :department_id', { department_id });
    }

    depositQueryBuilder.groupBy(`${dateFormat}`);

    // Get bet revenue (house edge)
    const betQueryBuilder = this.betRepository
      .createQueryBuilder('b')
      .select([
        `${dateFormat} as date`,
        'SUM(b.bet_amount - b.win_amount) as bet_revenue'
      ])
      .where("b.status IN ('won', 'lost')");

    // Apply filters for bet query
    if (from_date) {
      betQueryBuilder.andWhere('b.created_at >= :from_date', { from_date });
    }
    if (to_date) {
      betQueryBuilder.andWhere('b.created_at <= :to_date', { to_date });
    }
    if (department_id) {
      betQueryBuilder.andWhere('b.department_id = :department_id', { department_id });
    }

    betQueryBuilder.groupBy(`${dateFormat}`);

    const [depositResults, betResults] = await Promise.all([
      depositQueryBuilder.getRawMany(),
      betQueryBuilder.getRawMany()
    ]);

    // Combine results
    const combinedResults = new Map<string, any>();
    
    depositResults.forEach(row => {
      combinedResults.set(row.date, {
        date: row.date,
        deposit_revenue: parseFloat(row.deposit_revenue) || 0,
        bet_revenue: 0
      });
    });

    betResults.forEach(row => {
      const existing = combinedResults.get(row.date) || { 
        date: row.date, 
        deposit_revenue: 0, 
        bet_revenue: 0 
      };
      existing.bet_revenue = parseFloat(row.bet_revenue) || 0;
      combinedResults.set(row.date, existing);
    });

    const result: RevenueChartResponseDto[] = Array.from(combinedResults.values())
      .map(row => {
        const total_revenue = row.deposit_revenue + row.bet_revenue;
        const profit = row.bet_revenue; // Assuming bet revenue is profit
        const profit_margin = total_revenue > 0 ? (profit / total_revenue) * 100 : 0;

        return {
          date: row.date,
          deposit_revenue: row.deposit_revenue,
          bet_revenue: row.bet_revenue,
          total_revenue,
          profit,
          profit_margin: parseFloat(profit_margin.toFixed(2))
        };
      })
      .sort((a, b) => b.date.localeCompare(a.date));

    return result;
  }

  /**
   * Get date format based on chart type
   */
  private getDateFormat(chartType: ChartType): string {
    switch (chartType) {
      case ChartType.DAILY:
        return 'DATE(created_at)';
      case ChartType.WEEKLY:
        return 'YEARWEEK(created_at)';
      case ChartType.MONTHLY:
        return 'DATE_FORMAT(created_at, "%Y-%m")';
      default:
        return 'DATE(created_at)';
    }
  }
} 