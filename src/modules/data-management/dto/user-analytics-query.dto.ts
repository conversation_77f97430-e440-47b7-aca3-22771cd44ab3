import { IsOptional, IsString, IsEnum, IsNumber, IsDateString, IsUUID, Min, Max, IsBoolean } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { RegistrationSource, MemberStatus, ActivityLevel, GeographicRegion } from '../../../entities/user-analytics.entity';

export class UserAnalyticsQueryDto {
  @ApiPropertyOptional({ description: 'Số trang', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Số lượng mỗi trang', minimum: 1, maximum: 100, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Tì<PERSON> kiếm theo tên người dùng, email hoặc mã giới thiệu' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Lọc theo ID người dùng' })
  @IsOptional()
  @IsUUID()
  user_id?: string;

  @ApiPropertyOptional({ description: 'Lọc theo ID phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;

  @ApiPropertyOptional({ enum: RegistrationSource, description: 'Lọc theo nguồn đăng ký' })
  @IsOptional()
  @IsEnum(RegistrationSource)
  registration_source?: RegistrationSource;

  @ApiPropertyOptional({ enum: MemberStatus, description: 'Lọc theo trạng thái thành viên' })
  @IsOptional()
  @IsEnum(MemberStatus)
  member_status?: MemberStatus;

  @ApiPropertyOptional({ enum: ActivityLevel, description: 'Lọc theo mức độ hoạt động' })
  @IsOptional()
  @IsEnum(ActivityLevel)
  activity_level?: ActivityLevel;

  @ApiPropertyOptional({ enum: GeographicRegion, description: 'Lọc theo khu vực địa lý' })
  @IsOptional()
  @IsEnum(GeographicRegion)
  region?: GeographicRegion;

  // Date filters
  @ApiPropertyOptional({ description: 'Ngày đăng ký (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  registration_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày đăng ký (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  registration_date_to?: string;

  @ApiPropertyOptional({ description: 'Ngày đăng nhập cuối (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  last_login_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày đăng nhập cuối (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  last_login_date_to?: string;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền đầu tiên (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  first_deposit_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền đầu tiên (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  first_deposit_date_to?: string;

  // Numeric filters
  @ApiPropertyOptional({ description: 'Hạng thành viên tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_member_tier?: number;

  @ApiPropertyOptional({ description: 'Hạng thành viên tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_member_tier?: number;

  @ApiPropertyOptional({ description: 'Giá trị lifetime tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_lifetime_value?: number;

  @ApiPropertyOptional({ description: 'Giá trị lifetime tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_lifetime_value?: number;

  @ApiPropertyOptional({ description: 'Xác suất churn tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  min_churn_probability?: number;

  @ApiPropertyOptional({ description: 'Xác suất churn tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  max_churn_probability?: number;

  @ApiPropertyOptional({ description: 'Số lần đăng nhập tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_total_logins?: number;

  @ApiPropertyOptional({ description: 'Số lần đăng nhập tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_total_logins?: number;

  @ApiPropertyOptional({ description: 'Số ngày kể từ đăng nhập cuối tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_days_since_last_login?: number;

  @ApiPropertyOptional({ description: 'Số ngày kể từ đăng nhập cuối tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_days_since_last_login?: number;

  @ApiPropertyOptional({ description: 'Tổng tiền gửi tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_total_deposits?: number;

  @ApiPropertyOptional({ description: 'Tổng tiền gửi tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_total_deposits?: number;

  @ApiPropertyOptional({ description: 'Số lần gửi tiền tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_deposit_count?: number;

  @ApiPropertyOptional({ description: 'Số lần gửi tiền tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_deposit_count?: number;

  @ApiPropertyOptional({ description: 'Số tiền cược tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_total_bet_amount?: number;

  @ApiPropertyOptional({ description: 'Số tiền cược tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_total_bet_amount?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ thắng tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  min_win_rate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ thắng tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  @Max(100)
  max_win_rate?: number;

  // Boolean filters
  @ApiPropertyOptional({ description: 'Chỉ hiển thị người dùng mới' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  new_users_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị người dùng hoạt động' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  active_users_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị khách hàng có giá trị cao' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  high_value_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị khách hàng có nguy cơ rời bỏ' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  at_risk_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị khách hàng đã gửi tiền' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  deposited_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị KYC đã xác minh' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  kyc_verified_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị hoạt động đáng nghi' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  suspicious_only?: boolean;

  // Text filters
  @ApiPropertyOptional({ description: 'Lọc theo quốc gia' })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({ description: 'Lọc theo thành phố' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ description: 'Lọc theo nhóm tuổi' })
  @IsOptional()
  @IsString()
  age_group?: string;

  @ApiPropertyOptional({ description: 'Lọc theo giới tính' })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiPropertyOptional({ description: 'Lọc theo chiến dịch đăng ký' })
  @IsOptional()
  @IsString()
  registration_campaign?: string;

  @ApiPropertyOptional({ description: 'Lọc theo UTM Source' })
  @IsOptional()
  @IsString()
  utm_source?: string;

  @ApiPropertyOptional({ description: 'Lọc theo UTM Campaign' })
  @IsOptional()
  @IsString()
  utm_campaign?: string;

  @ApiPropertyOptional({ description: 'Lọc theo phân khúc khách hàng' })
  @IsOptional()
  @IsString()
  customer_segment?: string;

  // Sorting
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: [
      'registration_date',
      'last_login_date',
      'member_status',
      'activity_level',
      'lifetime_value',
      'churn_probability',
      'total_logins',
      'days_since_last_login',
      'total_deposits',
      'deposit_count',
      'first_deposit_amount',
      'total_bet_amount',
      'win_rate',
      'engagement_score',
      'created_at',
      'updated_at'
    ]
  })
  @IsOptional()
  @IsString()
  sort_by?: string;

  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp',
    enum: ['ASC', 'DESC'],
    default: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sort_order?: 'ASC' | 'DESC' = 'DESC';
} 