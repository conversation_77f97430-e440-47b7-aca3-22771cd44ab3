### Affiliate Reports API Examples

# 1. <PERSON><PERSON><PERSON> cáo tổng hợp - <PERSON><PERSON><PERSON><PERSON> hiện tại
GET {{baseUrl}}/affiliate/reports/comprehensive?period=monthly
Authorization: Bearer {{token}}

###

# 2. <PERSON><PERSON><PERSON> c<PERSON>o tổng hợp - <PERSON><PERSON>n hiện tại
GET {{baseUrl}}/affiliate/reports/comprehensive?period=weekly
Authorization: Bearer {{token}}

###

# 3. <PERSON><PERSON><PERSON> cáo tổng hợp - Năm hiện tại
GET {{baseUrl}}/affiliate/reports/comprehensive?period=yearly
Authorization: Bearer {{token}}

###

# 4. <PERSON><PERSON>o cáo tổng hợp - <PERSON><PERSON>ảng thời gian tùy chỉnh
GET {{baseUrl}}/affiliate/reports/comprehensive?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{token}}

###

# 5. B<PERSON>o cáo tổng hợp - Chỉ affiliate đang hoạt động
GET {{baseUrl}}/affiliate/reports/comprehensive?status=active&period=monthly
Authorization: Bearer {{token}}

###

# 6. <PERSON><PERSON><PERSON> c<PERSON><PERSON> tổng hợp - <PERSON> danh mục
GET {{baseUrl}}/affiliate/reports/comprehensive?category=premium&period=monthly
Authorization: Bearer {{token}}

###

# 7. Báo cáo tổng hợp - Cho affiliate cụ thể
GET {{baseUrl}}/affiliate/reports/comprehensive?affiliateCode=AFF001&period=monthly
Authorization: Bearer {{token}}

###

# 8. Báo cáo tổng hợp - Cho user cụ thể
GET {{baseUrl}}/affiliate/reports/comprehensive?userId={{userId}}&period=monthly
Authorization: Bearer {{token}}

###

# 9. Báo cáo tóm tắt
GET {{baseUrl}}/affiliate/reports/summary?period=monthly
Authorization: Bearer {{token}}

###

# 10. Báo cáo hiệu suất
GET {{baseUrl}}/affiliate/reports/performance?period=monthly
Authorization: Bearer {{token}}

###

# 11. Báo cáo doanh thu
GET {{baseUrl}}/affiliate/reports/revenue?period=monthly
Authorization: Bearer {{token}}

###

# 12. Báo cáo chuyển đổi
GET {{baseUrl}}/affiliate/reports/conversion?period=monthly
Authorization: Bearer {{token}}

###

# 13. Báo cáo tổng hợp với tất cả filters
GET {{baseUrl}}/affiliate/reports/comprehensive?period=monthly&status=active&category=premium&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{token}}

###

# 14. Báo cáo hiệu suất cho affiliate cụ thể
GET {{baseUrl}}/affiliate/reports/performance?affiliateCode=AFF001&period=weekly
Authorization: Bearer {{token}}

###

# 15. Báo cáo doanh thu theo ngày
GET {{baseUrl}}/affiliate/reports/revenue?period=daily&startDate=2024-01-01&endDate=2024-01-07
Authorization: Bearer {{token}}

###

# Variables for testing
# @baseUrl = http://localhost:3000
# @token = your-jwt-token-here
# @userId = user-uuid-here 