import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateController } from './affiliate.controller';
import { AffiliateService } from '../services/affiliate.service';
import { AffiliateUsageService } from '../services/affiliate-usage.service';
import { AffiliateReportQueryDto, AffiliateReportPeriod, AffiliateReportType } from '../dto/affiliate-report.dto';

describe('AffiliateController', () => {
  let controller: AffiliateController;
  let affiliateService: AffiliateService;
  let affiliateUsageService: AffiliateUsageService;

  const mockAffiliateService = {
    createAffiliate: jest.fn(),
    updateAffiliate: jest.fn(),
    toggleAffiliateStatus: jest.fn(),
    findByCode: jest.fn(),
    getAffiliateStats: jest.fn(),
    getUserAffiliates: jest.fn(),
    getReferredUsers: jest.fn(),
    deleteAffiliate: jest.fn(),
    generateAffiliateReport: jest.fn(),
  };

  const mockAffiliateUsageService = {
    getUsageHistory: jest.fn(),
    getTopPerformingAffiliates: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateController],
      providers: [
        {
          provide: AffiliateService,
          useValue: mockAffiliateService,
        },
        {
          provide: AffiliateUsageService,
          useValue: mockAffiliateUsageService,
        },
      ],
    }).compile();

    controller = module.get<AffiliateController>(AffiliateController);
    affiliateService = module.get<AffiliateService>(AffiliateService);
    affiliateUsageService = module.get<AffiliateUsageService>(AffiliateUsageService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('generateComprehensiveReport', () => {
    it('should generate comprehensive affiliate report', async () => {
      const query: AffiliateReportQueryDto = {
        period: AffiliateReportPeriod.MONTHLY,
        reportType: AffiliateReportType.SUMMARY,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 10,
          activeAffiliates: 8,
          totalUsageCount: 150,
          totalSuccessfulReferrals: 45,
          totalCommissionEarned: 1250.50,
          averageConversionRate: 30.0,
          totalTransactionValue: 5000.00,
          periodStart: new Date('2024-01-01'),
          periodEnd: new Date('2024-01-31'),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.MONTHLY,
        reportType: AffiliateReportType.SUMMARY,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generateComprehensiveReport(query);

      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });

    it('should handle custom date range', async () => {
      const query: AffiliateReportQueryDto = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        reportType: AffiliateReportType.PERFORMANCE,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 5,
          activeAffiliates: 4,
          totalUsageCount: 75,
          totalSuccessfulReferrals: 25,
          totalCommissionEarned: 625.25,
          averageConversionRate: 33.33,
          totalTransactionValue: 2500.00,
          periodStart: new Date('2024-01-01'),
          periodEnd: new Date('2024-01-31'),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.CUSTOM,
        reportType: AffiliateReportType.PERFORMANCE,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generateComprehensiveReport(query);

      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });
  });

  describe('generateSummaryReport', () => {
    it('should generate summary report with correct report type', async () => {
      const query: AffiliateReportQueryDto = {
        period: AffiliateReportPeriod.WEEKLY,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 10,
          activeAffiliates: 8,
          totalUsageCount: 150,
          totalSuccessfulReferrals: 45,
          totalCommissionEarned: 1250.50,
          averageConversionRate: 30.0,
          totalTransactionValue: 5000.00,
          periodStart: new Date(),
          periodEnd: new Date(),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.WEEKLY,
        reportType: AffiliateReportType.SUMMARY,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generateSummaryReport(query);

      expect(query.reportType).toBe(AffiliateReportType.SUMMARY);
      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });
  });

  describe('generatePerformanceReport', () => {
    it('should generate performance report with correct report type', async () => {
      const query: AffiliateReportQueryDto = {
        period: AffiliateReportPeriod.MONTHLY,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 10,
          activeAffiliates: 8,
          totalUsageCount: 150,
          totalSuccessfulReferrals: 45,
          totalCommissionEarned: 1250.50,
          averageConversionRate: 30.0,
          totalTransactionValue: 5000.00,
          periodStart: new Date(),
          periodEnd: new Date(),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.MONTHLY,
        reportType: AffiliateReportType.PERFORMANCE,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generatePerformanceReport(query);

      expect(query.reportType).toBe(AffiliateReportType.PERFORMANCE);
      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });
  });

  describe('generateRevenueReport', () => {
    it('should generate revenue report with correct report type', async () => {
      const query: AffiliateReportQueryDto = {
        period: AffiliateReportPeriod.YEARLY,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 10,
          activeAffiliates: 8,
          totalUsageCount: 150,
          totalSuccessfulReferrals: 45,
          totalCommissionEarned: 1250.50,
          averageConversionRate: 30.0,
          totalTransactionValue: 5000.00,
          periodStart: new Date(),
          periodEnd: new Date(),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.YEARLY,
        reportType: AffiliateReportType.REVENUE,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generateRevenueReport(query);

      expect(query.reportType).toBe(AffiliateReportType.REVENUE);
      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });
  });

  describe('generateConversionReport', () => {
    it('should generate conversion report with correct report type', async () => {
      const query: AffiliateReportQueryDto = {
        period: AffiliateReportPeriod.DAILY,
      };

      const mockReport = {
        summary: {
          totalAffiliates: 10,
          activeAffiliates: 8,
          totalUsageCount: 150,
          totalSuccessfulReferrals: 45,
          totalCommissionEarned: 1250.50,
          averageConversionRate: 30.0,
          totalTransactionValue: 5000.00,
          periodStart: new Date(),
          periodEnd: new Date(),
        },
        topPerformers: [],
        revenueBreakdown: [],
        conversionAnalysis: [],
        generatedAt: new Date(),
        period: AffiliateReportPeriod.DAILY,
        reportType: AffiliateReportType.CONVERSION,
      };

      mockAffiliateService.generateAffiliateReport.mockResolvedValue(mockReport);

      const result = await controller.generateConversionReport(query);

      expect(query.reportType).toBe(AffiliateReportType.CONVERSION);
      expect(mockAffiliateService.generateAffiliateReport).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockReport);
    });
  });
}); 