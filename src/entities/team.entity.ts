import { Entity, Column, ManyToOne, OneToMany, <PERSON>inC<PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { User } from './user.entity';

/**
 * Team Entity
 * Represents teams within departments
 */
@Entity('teams')
export class Team extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the team',
  })
  name: string;

  @Column({
    type: 'uuid',
    comment: 'Department ID that this team belongs to',
  })
  departmentId: string;

  // Relationships
  @ManyToOne(() => Department, (department) => department.teams)
  @JoinColumn({ name: 'departmentId' })
  department: Department;

  @OneToMany(() => User, (user) => user.team)
  users: User[];
}
