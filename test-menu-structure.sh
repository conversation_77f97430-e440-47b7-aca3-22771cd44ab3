#!/bin/bash

echo "=== Testing User Menu Structure API ==="

TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao"

echo "1. Testing with user who has assignments..."
RESPONSE1=$(curl -s -X GET \
  "http://localhost:3000/api/v1/user-menu-assignments/user/dab11ee1-d7c5-4e93-9a3e-3ac898f1b1d2/menu-structure" \
  -H "Authorization: Bearer $TOKEN")

echo "Response:"
echo "$RESPONSE1" | jq '.' 2>/dev/null || echo "$RESPONSE1"
echo ""

echo "2. Testing with user who might not have assignments..."
RESPONSE2=$(curl -s -X GET \
  "http://localhost:3000/api/v1/user-menu-assignments/user/3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367/menu-structure" \
  -H "Authorization: Bearer $TOKEN")

echo "Response:"
echo "$RESPONSE2" | jq '.' 2>/dev/null || echo "$RESPONSE2"
echo ""

echo "3. Testing with non-existent user (should return 404)..."
RESPONSE3=$(curl -s -X GET \
  "http://localhost:3000/api/v1/user-menu-assignments/user/00000000-0000-0000-0000-000000000000/menu-structure" \
  -H "Authorization: Bearer $TOKEN")

echo "Response:"
echo "$RESPONSE3" | jq '.' 2>/dev/null || echo "$RESPONSE3"
echo ""

echo "=== Testing Complete ==="
