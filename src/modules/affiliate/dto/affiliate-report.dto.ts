import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDateString, IsEnum, IsString, IsIn } from 'class-validator';

export enum AffiliateReportPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  CUSTOM = 'custom',
}

export enum AffiliateReportType {
  SUMMARY = 'summary',
  DETAILED = 'detailed',
  PERFORMANCE = 'performance',
  REVENUE = 'revenue',
  CONVERSION = 'conversion',
}

export class AffiliateReportQueryDto {
  @ApiProperty({
    description: 'Start date for the report period',
    example: '2024-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'End date for the report period',
    example: '2024-12-31',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Report period type',
    enum: AffiliateReportPeriod,
    example: AffiliateReportPeriod.MONTHLY,
    required: false,
  })
  @IsOptional()
  @IsEnum(AffiliateReportPeriod)
  period?: AffiliateReportPeriod;

  @ApiProperty({
    description: 'Report type',
    enum: AffiliateReportType,
    example: AffiliateReportType.SUMMARY,
    required: false,
  })
  @IsOptional()
  @IsEnum(AffiliateReportType)
  reportType?: AffiliateReportType;

  @ApiProperty({
    description: 'Affiliate code to filter by',
    example: 'AFF001',
    required: false,
  })
  @IsOptional()
  @IsString()
  affiliateCode?: string;

  @ApiProperty({
    description: 'User ID to filter by',
    example: 'uuid',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Category to filter by',
    example: 'general',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: 'Status to filter by (active/inactive)',
    example: 'active',
    required: false,
    enum: ['active', 'inactive'],
  })
  @IsOptional()
  @IsIn(['active', 'inactive'])
  status?: string;
}

export class AffiliateReportSummaryDto {
  @ApiProperty({ description: 'Total number of affiliates' })
  totalAffiliates: number;

  @ApiProperty({ description: 'Active affiliates count' })
  activeAffiliates: number;

  @ApiProperty({ description: 'Total usage count across all affiliates' })
  totalUsageCount: number;

  @ApiProperty({ description: 'Total successful referrals' })
  totalSuccessfulReferrals: number;

  @ApiProperty({ description: 'Total commission earned' })
  totalCommissionEarned: number;

  @ApiProperty({ description: 'Average conversion rate' })
  averageConversionRate: number;

  @ApiProperty({ description: 'Total transaction value' })
  totalTransactionValue: number;

  @ApiProperty({ description: 'Report period start date' })
  periodStart: Date;

  @ApiProperty({ description: 'Report period end date' })
  periodEnd: Date;
}

export class AffiliatePerformanceDto {
  @ApiProperty({ description: 'Affiliate ID' })
  affiliateId: string;

  @ApiProperty({ description: 'Affiliate code' })
  code: string;

  @ApiProperty({ description: 'Affiliate description' })
  description: string;

  @ApiProperty({ description: 'User ID who owns this affiliate' })
  userId: string;

  @ApiProperty({ description: 'Username of the affiliate owner' })
  username: string;

  @ApiProperty({ description: 'Usage count in the period' })
  usageCount: number;

  @ApiProperty({ description: 'Successful referrals in the period' })
  successfulReferrals: number;

  @ApiProperty({ description: 'Commission earned in the period' })
  commissionEarned: number;

  @ApiProperty({ description: 'Transaction value in the period' })
  transactionValue: number;

  @ApiProperty({ description: 'Conversion rate in the period' })
  conversionRate: number;

  @ApiProperty({ description: 'Commission rate' })
  commissionRate: number;

  @ApiProperty({ description: 'Whether affiliate is active' })
  isActive: boolean;

  @ApiProperty({ description: 'Category of the affiliate' })
  category: string;
}

export class AffiliateRevenueDto {
  @ApiProperty({ description: 'Date' })
  date: string;

  @ApiProperty({ description: 'Commission earned on this date' })
  commissionEarned: number;

  @ApiProperty({ description: 'Transaction value on this date' })
  transactionValue: number;

  @ApiProperty({ description: 'Number of successful referrals' })
  successfulReferrals: number;

  @ApiProperty({ description: 'Total usage count' })
  usageCount: number;
}

export class AffiliateConversionDto {
  @ApiProperty({ description: 'Affiliate ID' })
  affiliateId: string;

  @ApiProperty({ description: 'Affiliate code' })
  code: string;

  @ApiProperty({ description: 'Total usage count' })
  totalUsageCount: number;

  @ApiProperty({ description: 'Successful referrals count' })
  successfulReferrals: number;

  @ApiProperty({ description: 'Conversion rate percentage' })
  conversionRate: number;

  @ApiProperty({ description: 'Average time to conversion in hours' })
  averageConversionTime: number;

  @ApiProperty({ description: 'Top performing countries' })
  topCountries: Array<{ country: string; count: number }>;

  @ApiProperty({ description: 'Top performing devices' })
  topDevices: Array<{ device: string; count: number }>;
}

export class AffiliateReportResponseDto {
  @ApiProperty({ description: 'Report summary' })
  summary: AffiliateReportSummaryDto;

  @ApiProperty({ description: 'Top performing affiliates', type: [AffiliatePerformanceDto] })
  topPerformers: AffiliatePerformanceDto[];

  @ApiProperty({ description: 'Revenue breakdown by period', type: [AffiliateRevenueDto] })
  revenueBreakdown: AffiliateRevenueDto[];

  @ApiProperty({ description: 'Conversion analysis', type: [AffiliateConversionDto] })
  conversionAnalysis: AffiliateConversionDto[];

  @ApiProperty({ description: 'Report generation timestamp' })
  generatedAt: Date;

  @ApiProperty({ description: 'Report period' })
  period: AffiliateReportPeriod;

  @ApiProperty({ description: 'Report type' })
  reportType: AffiliateReportType;
} 