import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString } from 'class-validator';

export class BetChartDto {
  @ApiProperty({ description: 'Từ ngày', required: false })
  @IsOptional()
  @IsDateString()
  from_date?: string;

  @ApiProperty({ description: 'Đến ngày', required: false })
  @IsOptional()
  @IsDateString()
  to_date?: string;

  @ApiProperty({ description: 'ID bộ phận', required: false })
  @IsOptional()
  @IsString()
  department_id?: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> biểu đồ', enum: ['daily', 'weekly', 'monthly'], default: 'daily' })
  @IsOptional()
  @IsString()
  chart_type?: string;
} 