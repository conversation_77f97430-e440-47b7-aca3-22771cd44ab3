import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsEnum, IsDateString, IsArray, IsBoolean, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { CostType, CostStatus } from '../../../entities/cost.entity';

export class CreateCostDto {
  @ApiProperty({ description: 'Tên chi phí' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Mô tả chi phí', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Số tiền chi phí', minimum: 0 })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({ description: 'Đơn vị tiền tệ', default: 'VND' })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ description: 'Loại chi phí', enum: CostType, default: CostType.OTHER })
  @IsOptional()
  @IsEnum(CostType)
  type?: CostType;

  @ApiProperty({ description: 'Trạng thái chi phí', enum: CostStatus, default: CostStatus.PENDING })
  @IsOptional()
  @IsEnum(CostStatus)
  status?: CostStatus;

  @ApiProperty({ description: 'Ngày phát sinh chi phí (YYYY-MM-DD)' })
  @IsNotEmpty()
  @IsDateString()
  costDate: string;

  @ApiProperty({ description: 'Ngày thanh toán (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  paymentDate?: string;

  @ApiProperty({ description: 'Nhà cung cấp/Vendor', required: false })
  @IsOptional()
  @IsString()
  vendor?: string;

  @ApiProperty({ description: 'Mã hóa đơn', required: false })
  @IsOptional()
  @IsString()
  invoiceNumber?: string;

  @ApiProperty({ description: 'Link Google Drive chứa chứng từ', required: false })
  @IsOptional()
  @IsString()
  googleDriveLink?: string;

  @ApiProperty({ description: 'Danh sách links Google Drive bổ sung', type: [String], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  additionalLinks?: string[];

  @ApiProperty({ description: 'Tên file trong Google Drive', required: false })
  @IsOptional()
  @IsString()
  fileName?: string;

  @ApiProperty({ description: 'Loại file (pdf, xlsx, doc, etc.)', required: false })
  @IsOptional()
  @IsString()
  fileType?: string;

  @ApiProperty({ description: 'Ghi chú thêm', required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: 'ID bộ phận', required: false })
  @IsOptional()
  @IsString()
  department_id?: string;
} 