import { Entity, PrimaryGeneratedColumn, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Department } from './department.entity';
import { User } from './user.entity';

export enum AdPlatform {
  GOOGLE_ADS = 'google_ads',
  FACEBOOK_ADS = 'facebook_ads',
  INSTAGRAM_ADS = 'instagram_ads',
  TIKTOK_ADS = 'tiktok_ads',
  YOUTUBE_ADS = 'youtube_ads',
  TWITTER_ADS = 'twitter_ads',
  LINKEDIN_ADS = 'linkedin_ads',
  SNAPCHAT_ADS = 'snapchat_ads',
  PINTEREST_ADS = 'pinterest_ads',
  OTHER = 'other'
}

export enum AdType {
  DISPLAY = 'display',
  SEARCH = 'search',
  VIDEO = 'video',
  SHOPPING = 'shopping',
  SOCIAL = 'social',
  NATIVE = 'native',
  BANNER = 'banner',
  POPUP = 'popup',
  INTERSTITIAL = 'interstitial',
  RICH_MEDIA = 'rich_media'
}

export enum AdStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  PENDING = 'pending',
  REJECTED = 'rejected',
  COMPLETED = 'completed'
}

export enum AdObjective {
  TRAFFIC = 'traffic',
  CONVERSIONS = 'conversions',
  LEADS = 'leads',
  BRAND_AWARENESS = 'brand_awareness',
  REACH = 'reach',
  ENGAGEMENT = 'engagement',
  APP_INSTALLS = 'app_installs',
  VIDEO_VIEWS = 'video_views',
  MESSAGES = 'messages',
  CATALOG_SALES = 'catalog_sales'
}

@Entity('ad_performance')
export class AdPerformance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  campaign_name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  ad_set_name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  ad_name: string;

  @Column({
    type: 'enum',
    enum: AdPlatform,
    default: AdPlatform.OTHER
  })
  platform: AdPlatform;

  @Column({
    type: 'enum',
    enum: AdType,
    default: AdType.DISPLAY
  })
  ad_type: AdType;

  @Column({
    type: 'enum',
    enum: AdStatus,
    default: AdStatus.PENDING
  })
  status: AdStatus;

  @Column({
    type: 'enum',
    enum: AdObjective,
    default: AdObjective.TRAFFIC
  })
  objective: AdObjective;

  // Performance Metrics
  @Column({ type: 'bigint', default: 0 })
  impressions: number;

  @Column({ type: 'bigint', default: 0 })
  clicks: number;

  @Column({ type: 'bigint', default: 0 })
  conversions: number;

  @Column({ type: 'bigint', default: 0 })
  leads: number;

  @Column({ type: 'bigint', default: 0 })
  registrations: number;

  @Column({ type: 'bigint', default: 0 })
  deposits: number;

  @Column({ type: 'bigint', default: 0 })
  first_time_deposits: number;

  // Cost Metrics
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_cost: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_click: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_mille: number; // CPM

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_acquisition: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_lead: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_registration: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_deposit: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  cost_per_ftd: number;

  // Revenue Metrics
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_revenue: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  deposit_amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  ftd_amount: number;

  // Calculated Performance Ratios (stored for optimization)
  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  click_through_rate: number; // CTR = (clicks / impressions) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  conversion_rate: number; // CVR = (conversions / clicks) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  registration_rate: number; // (registrations / clicks) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  deposit_rate: number; // (deposits / registrations) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  ftd_rate: number; // (first_time_deposits / registrations) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  return_on_ad_spend: number; // ROAS = (revenue / cost) * 100

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  return_on_investment: number; // ROI = ((revenue - cost) / cost) * 100

  // Time Period
  @Column({ type: 'date' })
  start_date: Date;

  @Column({ type: 'date' })
  end_date: Date;

  @Column({ type: 'date' })
  report_date: Date;

  // Targeting Information
  @Column({ type: 'varchar', length: 255, nullable: true })
  target_audience: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  target_location: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  age_range: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  gender: string;

  @Column({ type: 'text', nullable: true })
  interests: string;

  @Column({ type: 'text', nullable: true })
  keywords: string;

  @Column({ type: 'varchar', length: 10, default: 'VND' })
  currency: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  campaign_url: string;

  @Column({ type: 'text', nullable: true })
  landing_page_url: string;

  // Relationships
  @Column({ type: 'uuid', nullable: true })
  department_id: string;

  @ManyToOne(() => Department, department => department.id)
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({ type: 'uuid', nullable: true })
  created_by: string;

  @ManyToOne(() => User, user => user.id)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ type: 'uuid', nullable: true })
  updated_by: string;

  @ManyToOne(() => User, user => user.id)
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Computed Properties
  get formattedTotalCost(): string {
    return `${this.total_cost.toLocaleString()} ${this.currency}`;
  }

  get formattedRevenue(): string {
    return `${this.total_revenue.toLocaleString()} ${this.currency}`;
  }

  get formattedCPA(): string {
    return `${this.cost_per_acquisition.toLocaleString()} ${this.currency}`;
  }

  get formattedCTR(): string {
    return `${this.click_through_rate.toFixed(2)}%`;
  }

  get formattedConversionRate(): string {
    return `${this.conversion_rate.toFixed(2)}%`;
  }

  get formattedROAS(): string {
    return `${this.return_on_ad_spend.toFixed(2)}%`;
  }

  get formattedROI(): string {
    return `${this.return_on_investment.toFixed(2)}%`;
  }

  get isActive(): boolean {
    return this.status === AdStatus.ACTIVE;
  }

  get isProfitable(): boolean {
    return this.return_on_investment > 0;
  }

  get campaignDuration(): number {
    const start = new Date(this.start_date);
    const end = new Date(this.end_date);
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  }

  get dailyAverageCost(): number {
    const duration = this.campaignDuration;
    return duration > 0 ? this.total_cost / duration : 0;
  }

  get dailyAverageRevenue(): number {
    const duration = this.campaignDuration;
    return duration > 0 ? this.total_revenue / duration : 0;
  }

  get effectivenessScore(): number {
    // Composite score based on CTR, CVR, and ROI
    const ctrScore = Math.min(this.click_through_rate * 10, 100);
    const cvrScore = Math.min(this.conversion_rate * 5, 100);
    const roiScore = Math.min(Math.max(this.return_on_investment, 0), 100);
    return (ctrScore + cvrScore + roiScore) / 3;
  }

  // Methods for calculating metrics
  calculateCTR(): number {
    return this.impressions > 0 ? (this.clicks / this.impressions) * 100 : 0;
  }

  calculateCVR(): number {
    return this.clicks > 0 ? (this.conversions / this.clicks) * 100 : 0;
  }

  calculateCPA(): number {
    return this.conversions > 0 ? this.total_cost / this.conversions : 0;
  }

  calculateROAS(): number {
    return this.total_cost > 0 ? (this.total_revenue / this.total_cost) * 100 : 0;
  }

  calculateROI(): number {
    return this.total_cost > 0 ? ((this.total_revenue - this.total_cost) / this.total_cost) * 100 : 0;
  }

  updateCalculatedMetrics(): void {
    this.click_through_rate = this.calculateCTR();
    this.conversion_rate = this.calculateCVR();
    this.cost_per_acquisition = this.calculateCPA();
    this.return_on_ad_spend = this.calculateROAS();
    this.return_on_investment = this.calculateROI();
  }
} 