import { Entity, PrimaryGeneratedColumn, Column, <PERSON>ToOne, Join<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { User } from './user.entity';
import { Department } from './department.entity';

export enum RegistrationSource {
  WEBSITE = 'website',
  MOBILE_APP = 'mobile_app',
  SOCIAL_MEDIA = 'social_media',
  REFERRAL = 'referral',
  ADVERTISEMENT = 'advertisement',
  EMAIL_CAMPAIGN = 'email_campaign',
  SMS_CAMPAIGN = 'sms_campaign',
  AFFILIATE = 'affiliate',
  ORGANIC_SEARCH = 'organic_search',
  PAID_SEARCH = 'paid_search',
  DIRECT = 'direct',
  OTHER = 'other'
}

export enum MemberStatus {
  NEW = 'new',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  VIP = 'vip',
  SUSPENDED = 'suspended',
  BANNED = 'banned',
  CHURNED = 'churned',
  HIGH_VALUE = 'high_value',
  AT_RISK = 'at_risk'
}

export enum ActivityLevel {
  VERY_LOW = 'very_low',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high'
}

export enum GeographicRegion {
  NORTH = 'north',
  SOUTH = 'south',
  CENTRAL = 'central',
  NORTHEAST = 'northeast',
  NORTHWEST = 'northwest',
  SOUTHEAST = 'southeast',
  SOUTHWEST = 'southwest',
  MEKONG_DELTA = 'mekong_delta',
  INTERNATIONAL = 'international'
}

@Entity('user_analytics')
@Index(['user_id'])
@Index(['registration_date'])
@Index(['member_status'])
@Index(['department_id'])
@Index(['registration_source'])
export class UserAnalytics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Registration Information
  @Column({ type: 'date' })
  registration_date: Date;

  @Column({
    type: 'enum',
    enum: RegistrationSource,
    default: RegistrationSource.WEBSITE
  })
  registration_source: RegistrationSource;

  @Column({ type: 'varchar', length: 255, nullable: true })
  registration_campaign: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  referral_code: string;

  @Column({ type: 'uuid', nullable: true })
  referred_by_user_id: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'referred_by_user_id' })
  referred_by_user: User;

  @Column({ type: 'varchar', length: 100, nullable: true })
  registration_ip: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  registration_device: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  registration_browser: string;

  // Member Status & Classification
  @Column({
    type: 'enum',
    enum: MemberStatus,
    default: MemberStatus.NEW
  })
  member_status: MemberStatus;

  @Column({ type: 'date', nullable: true })
  status_updated_date: Date;

  @Column({
    type: 'enum',
    enum: ActivityLevel,
    default: ActivityLevel.LOW
  })
  activity_level: ActivityLevel;

  @Column({ type: 'int', default: 0 })
  member_tier: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  lifetime_value: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  predicted_value: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  churn_probability: number;

  // Activity Metrics
  @Column({ type: 'int', default: 0 })
  total_logins: number;

  @Column({ type: 'date', nullable: true })
  last_login_date: Date;

  @Column({ type: 'int', default: 0 })
  days_since_last_login: number;

  @Column({ type: 'int', default: 0 })
  total_sessions: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  avg_session_duration: number;

  @Column({ type: 'int', default: 0 })
  page_views: number;

  // Financial Metrics
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_deposits: number;

  @Column({ type: 'int', default: 0 })
  deposit_count: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  first_deposit_amount: number;

  @Column({ type: 'date', nullable: true })
  first_deposit_date: Date;

  @Column({ type: 'date', nullable: true })
  last_deposit_date: Date;

  @Column({ type: 'int', default: 0 })
  days_to_first_deposit: number;

  @Column({ type: 'int', default: 0 })
  days_since_last_deposit: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  avg_deposit_amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_withdrawals: number;

  @Column({ type: 'int', default: 0 })
  withdrawal_count: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  current_balance: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_winnings: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_losses: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 0 })
  win_loss_ratio: number;

  // Betting Metrics
  @Column({ type: 'int', default: 0 })
  total_bets: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  total_bet_amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  avg_bet_amount: number;

  @Column({ type: 'int', default: 0 })
  winning_bets: number;

  @Column({ type: 'int', default: 0 })
  losing_bets: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  win_rate: number;

  @Column({ type: 'date', nullable: true })
  last_bet_date: Date;

  @Column({ type: 'int', default: 0 })
  days_since_last_bet: number;

  // Geographic & Demographic Data
  @Column({ type: 'varchar', length: 100, nullable: true })
  country: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({
    type: 'enum',
    enum: GeographicRegion,
    nullable: true
  })
  region: GeographicRegion;

  @Column({ type: 'varchar', length: 10, nullable: true })
  age_group: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  gender: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  preferred_language: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  timezone: string;

  // Communication Preferences
  @Column({ type: 'boolean', default: true })
  email_notifications: boolean;

  @Column({ type: 'boolean', default: true })
  sms_notifications: boolean;

  @Column({ type: 'boolean', default: false })
  push_notifications: boolean;

  @Column({ type: 'boolean', default: false })
  marketing_emails: boolean;

  @Column({ type: 'int', default: 0 })
  email_opens: number;

  @Column({ type: 'int', default: 0 })
  email_clicks: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  email_open_rate: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  email_click_rate: number;

  // Risk & Compliance
  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  risk_score: number;

  @Column({ type: 'boolean', default: false })
  kyc_verified: boolean;

  @Column({ type: 'date', nullable: true })
  kyc_verified_date: Date;

  @Column({ type: 'boolean', default: false })
  suspicious_activity: boolean;

  @Column({ type: 'int', default: 0 })
  compliance_flags: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  // Segmentation Tags
  @Column({ type: 'text', nullable: true })
  tags: string; // JSON array of tags

  @Column({ type: 'varchar', length: 100, nullable: true })
  customer_segment: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  marketing_persona: string;

  // Attribution & Campaign Data
  @Column({ type: 'varchar', length: 255, nullable: true })
  utm_source: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  utm_medium: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  utm_campaign: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  utm_term: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  utm_content: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  acquisition_cost: number;

  // Department Assignment
  @Column({ type: 'uuid', nullable: true })
  department_id: string;

  @ManyToOne(() => Department, department => department.id)
  @JoinColumn({ name: 'department_id' })
  department: Department;

  // Timestamps
  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'datetime', nullable: true })
  last_calculated: Date;

  // Computed Properties
  get isNewUser(): boolean {
    const daysSinceRegistration = Math.floor((Date.now() - this.registration_date.getTime()) / (1000 * 60 * 60 * 24));
    return daysSinceRegistration <= 30;
  }

  get isActiveUser(): boolean {
    return this.days_since_last_login <= 7;
  }

  get isHighValue(): boolean {
    return this.lifetime_value > 10000000; // 10M VND
  }

  get isAtRisk(): boolean {
    return this.churn_probability > 70;
  }

  get hasDeposited(): boolean {
    return this.deposit_count > 0;
  }

  get daysSinceRegistration(): number {
    return Math.floor((Date.now() - this.registration_date.getTime()) / (1000 * 60 * 60 * 24));
  }

  get registrationToFirstDepositDays(): number | null {
    if (!this.first_deposit_date) return null;
    return Math.floor((this.first_deposit_date.getTime() - this.registration_date.getTime()) / (1000 * 60 * 60 * 24));
  }

  get avgSessionDurationMinutes(): number {
    return Math.round(this.avg_session_duration / 60);
  }

  get formattedLifetimeValue(): string {
    return `${this.lifetime_value.toLocaleString()} VND`;
  }

  get formattedTotalDeposits(): string {
    return `${this.total_deposits.toLocaleString()} VND`;
  }

  get profitLoss(): number {
    return this.total_winnings - this.total_losses;
  }

  get formattedProfitLoss(): string {
    const value = this.profitLoss;
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toLocaleString()} VND`;
  }

  get engagementScore(): number {
    // Calculate engagement score based on activity metrics
    const loginScore = Math.min(this.total_logins / 100, 1) * 30;
    const sessionScore = Math.min(this.total_sessions / 50, 1) * 25;
    const betScore = Math.min(this.total_bets / 100, 1) * 25;
    const recentActivityScore = this.days_since_last_login <= 7 ? 20 : 0;
    
    return Math.round(loginScore + sessionScore + betScore + recentActivityScore);
  }

  get customerLifetimeStage(): string {
    const daysSinceReg = this.daysSinceRegistration;
    
    if (daysSinceReg <= 7) return 'New';
    if (daysSinceReg <= 30) return 'Onboarding';
    if (daysSinceReg <= 90) return 'Early';
    if (daysSinceReg <= 365) return 'Established';
    return 'Veteran';
  }

  // Methods for updating analytics
  updateActivityMetrics(sessions: number, totalDuration: number, pageViews: number): void {
    this.total_sessions += sessions;
    this.avg_session_duration = (this.avg_session_duration * (this.total_sessions - sessions) + totalDuration) / this.total_sessions;
    this.page_views += pageViews;
    this.last_calculated = new Date();
  }

  updateFinancialMetrics(deposits: number, depositAmount: number, withdrawals: number, withdrawalAmount: number): void {
    this.deposit_count += deposits;
    this.total_deposits += depositAmount;
    this.withdrawal_count += withdrawals;
    this.total_withdrawals += withdrawalAmount;
    
    if (this.deposit_count > 0) {
      this.avg_deposit_amount = this.total_deposits / this.deposit_count;
    }
    
    this.last_calculated = new Date();
  }

  updateBettingMetrics(totalBets: number, totalAmount: number, winningBets: number, totalWinnings: number): void {
    this.total_bets += totalBets;
    this.total_bet_amount += totalAmount;
    this.winning_bets += winningBets;
    this.losing_bets += (totalBets - winningBets);
    this.total_winnings += totalWinnings;
    this.total_losses += (totalAmount - totalWinnings);
    
    if (this.total_bets > 0) {
      this.avg_bet_amount = this.total_bet_amount / this.total_bets;
      this.win_rate = (this.winning_bets / this.total_bets) * 100;
    }
    
    if (this.total_losses > 0) {
      this.win_loss_ratio = this.total_winnings / this.total_losses;
    }
    
    this.last_calculated = new Date();
  }

  calculateChurnProbability(): void {
    let score = 0;
    
    // Days since last login factor
    if (this.days_since_last_login > 30) score += 40;
    else if (this.days_since_last_login > 14) score += 25;
    else if (this.days_since_last_login > 7) score += 15;
    
    // Days since last deposit factor
    if (this.days_since_last_deposit > 60) score += 30;
    else if (this.days_since_last_deposit > 30) score += 20;
    else if (this.days_since_last_deposit > 14) score += 10;
    
    // Activity level factor
    if (this.activity_level === ActivityLevel.VERY_LOW) score += 20;
    else if (this.activity_level === ActivityLevel.LOW) score += 10;
    
    // Session frequency factor
    const avgSessionsPerDay = this.total_sessions / Math.max(this.daysSinceRegistration, 1);
    if (avgSessionsPerDay < 0.1) score += 10;
    
    this.churn_probability = Math.min(score, 100);
  }

  updateMemberStatus(): void {
    if (this.isAtRisk) {
      this.member_status = MemberStatus.AT_RISK;
    } else if (this.isHighValue) {
      this.member_status = MemberStatus.HIGH_VALUE;
    } else if (this.isActiveUser) {
      this.member_status = MemberStatus.ACTIVE;
    } else if (this.days_since_last_login > 30) {
      this.member_status = MemberStatus.INACTIVE;
    } else if (this.isNewUser) {
      this.member_status = MemberStatus.NEW;
    }
    
    this.status_updated_date = new Date();
  }
} 