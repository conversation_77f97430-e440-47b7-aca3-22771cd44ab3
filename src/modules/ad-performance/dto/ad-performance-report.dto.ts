import { IsOptional, IsString, IsEnum, IsDateString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AdPlatform, AdType, AdStatus, AdObjective } from '../../../entities/ad-performance.entity';

export class AdPerformanceReportQueryDto {
  @ApiPropertyOptional({ description: 'Ngày bắt đầu báo cáo', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON>y kết thúc báo cáo', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiPropertyOptional({ description: 'Lọc theo phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;

  @ApiPropertyOptional({ enum: AdPlatform, description: '<PERSON>ọ<PERSON> theo nền tảng' })
  @IsOptional()
  @IsEnum(AdPlatform)
  platform?: AdPlatform;

  @ApiPropertyOptional({ enum: AdType, description: 'Lọc theo loại quảng cáo' })
  @IsOptional()
  @IsEnum(AdType)
  ad_type?: AdType;

  @ApiPropertyOptional({ enum: AdStatus, description: 'Lọc theo trạng thái' })
  @IsOptional()
  @IsEnum(AdStatus)
  status?: AdStatus;

  @ApiPropertyOptional({ enum: AdObjective, description: 'Lọc theo mục tiêu' })
  @IsOptional()
  @IsEnum(AdObjective)
  objective?: AdObjective;

  @ApiPropertyOptional({
    description: 'Khoảng thời gian tính toán',
    enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
    default: 'daily'
  })
  @IsOptional()
  @IsString()
  time_period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' = 'daily';

  @ApiPropertyOptional({ description: 'So sánh với kỳ trước' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  compare_previous?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm phân tích theo phòng ban' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_department_analysis?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm phân tích theo nền tảng' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_platform_analysis?: boolean;

  @ApiPropertyOptional({ description: 'Bao gồm top performers' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_top_performers?: boolean;

  @ApiPropertyOptional({ description: 'Số lượng top performers', minimum: 1, maximum: 50, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  top_performers_limit?: number = 10;
}

export class AdPerformanceStatsDto {
  @ApiProperty({ description: 'Tổng số chiến dịch' })
  total_campaigns: number;

  @ApiProperty({ description: 'Số chiến dịch đang hoạt động' })
  active_campaigns: number;

  @ApiProperty({ description: 'Tổng số lần hiển thị' })
  total_impressions: number;

  @ApiProperty({ description: 'Tổng số lần click' })
  total_clicks: number;

  @ApiProperty({ description: 'Tổng số lần chuyển đổi' })
  total_conversions: number;

  @ApiProperty({ description: 'Tổng chi phí' })
  total_cost: number;

  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'Tỷ lệ click qua trung bình (%)' })
  avg_ctr: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi trung bình (%)' })
  avg_conversion_rate: number;

  @ApiProperty({ description: 'CPA trung bình' })
  avg_cpa: number;

  @ApiProperty({ description: 'ROAS trung bình (%)' })
  avg_roas: number;

  @ApiProperty({ description: 'ROI trung bình (%)' })
  avg_roi: number;

  @ApiProperty({ description: 'Tỷ lệ chiến dịch có lợi nhuận (%)' })
  profitable_campaigns_percentage: number;
}

export class AdPerformanceTimeSeriesDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số lần hiển thị' })
  impressions: number;

  @ApiProperty({ description: 'Số lần click' })
  clicks: number;

  @ApiProperty({ description: 'Số lần chuyển đổi' })
  conversions: number;

  @ApiProperty({ description: 'Chi phí' })
  cost: number;

  @ApiProperty({ description: 'Doanh thu' })
  revenue: number;

  @ApiProperty({ description: 'CTR (%)' })
  ctr: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi (%)' })
  conversion_rate: number;

  @ApiProperty({ description: 'CPA' })
  cpa: number;

  @ApiProperty({ description: 'ROAS (%)' })
  roas: number;

  @ApiProperty({ description: 'ROI (%)' })
  roi: number;
}

export class AdPerformancePlatformAnalysisDto {
  @ApiProperty({ enum: AdPlatform, description: 'Nền tảng' })
  platform: AdPlatform;

  @ApiProperty({ description: 'Tên nền tảng' })
  platform_name: string;

  @ApiProperty({ description: 'Số chiến dịch' })
  campaigns_count: number;

  @ApiProperty({ description: 'Tổng chi phí' })
  total_cost: number;

  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'Tổng số lần hiển thị' })
  total_impressions: number;

  @ApiProperty({ description: 'Tổng số lần click' })
  total_clicks: number;

  @ApiProperty({ description: 'Tổng số lần chuyển đổi' })
  total_conversions: number;

  @ApiProperty({ description: 'CTR trung bình (%)' })
  avg_ctr: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi trung bình (%)' })
  avg_conversion_rate: number;

  @ApiProperty({ description: 'CPA trung bình' })
  avg_cpa: number;

  @ApiProperty({ description: 'ROAS trung bình (%)' })
  avg_roas: number;

  @ApiProperty({ description: 'ROI trung bình (%)' })
  avg_roi: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng chi phí (%)' })
  cost_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng doanh thu (%)' })
  revenue_percentage: number;

  @ApiProperty({ description: 'Điểm hiệu quả' })
  effectiveness_score: number;
}

export class AdPerformanceDepartmentAnalysisDto {
  @ApiProperty({ description: 'ID phòng ban' })
  department_id: string;

  @ApiProperty({ description: 'Tên phòng ban' })
  department_name: string;

  @ApiProperty({ description: 'Số chiến dịch' })
  campaigns_count: number;

  @ApiProperty({ description: 'Tổng chi phí' })
  total_cost: number;

  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'Tổng số lần hiển thị' })
  total_impressions: number;

  @ApiProperty({ description: 'Tổng số lần click' })
  total_clicks: number;

  @ApiProperty({ description: 'Tổng số lần chuyển đổi' })
  total_conversions: number;

  @ApiProperty({ description: 'CTR trung bình (%)' })
  avg_ctr: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi trung bình (%)' })
  avg_conversion_rate: number;

  @ApiProperty({ description: 'CPA trung bình' })
  avg_cpa: number;

  @ApiProperty({ description: 'ROAS trung bình (%)' })
  avg_roas: number;

  @ApiProperty({ description: 'ROI trung bình (%)' })
  avg_roi: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng chi phí (%)' })
  cost_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ trong tổng doanh thu (%)' })
  revenue_percentage: number;

  @ApiProperty({ description: 'Điểm hiệu quả' })
  effectiveness_score: number;
}

export class AdPerformanceTopPerformerDto {
  @ApiProperty({ description: 'ID chiến dịch' })
  id: string;

  @ApiProperty({ description: 'Tên chiến dịch' })
  campaign_name: string;

  @ApiProperty({ enum: AdPlatform, description: 'Nền tảng' })
  platform: AdPlatform;

  @ApiProperty({ description: 'Tổng chi phí' })
  total_cost: number;

  @ApiProperty({ description: 'Tổng doanh thu' })
  total_revenue: number;

  @ApiProperty({ description: 'ROI (%)' })
  roi: number;

  @ApiProperty({ description: 'ROAS (%)' })
  roas: number;

  @ApiProperty({ description: 'CTR (%)' })
  ctr: number;

  @ApiProperty({ description: 'Tỷ lệ chuyển đổi (%)' })
  conversion_rate: number;

  @ApiProperty({ description: 'CPA' })
  cpa: number;

  @ApiProperty({ description: 'Điểm hiệu quả' })
  effectiveness_score: number;

  @ApiProperty({ description: 'Tên phòng ban' })
  department_name: string;
}

export class AdPerformanceComparisonDto {
  @ApiProperty({ description: 'Kỳ hiện tại' })
  current_period: AdPerformanceStatsDto;

  @ApiProperty({ description: 'Kỳ trước' })
  previous_period: AdPerformanceStatsDto;

  @ApiProperty({ description: 'Tỷ lệ thay đổi tổng chi phí (%)' })
  cost_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi tổng doanh thu (%)' })
  revenue_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi CTR (%)' })
  ctr_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi tỷ lệ chuyển đổi (%)' })
  conversion_rate_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi CPA (%)' })
  cpa_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi ROAS (%)' })
  roas_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi ROI (%)' })
  roi_change_percentage: number;

  @ApiProperty({ description: 'Tỷ lệ thay đổi số chiến dịch (%)' })
  campaigns_change_percentage: number;
}

export class AdPerformanceReportDto {
  @ApiProperty({ description: 'Thống kê tổng quan' })
  stats: AdPerformanceStatsDto;

  @ApiProperty({ type: [AdPerformanceTimeSeriesDto], description: 'Dữ liệu theo thời gian' })
  time_series: AdPerformanceTimeSeriesDto[];

  @ApiPropertyOptional({ type: [AdPerformancePlatformAnalysisDto], description: 'Phân tích theo nền tảng' })
  platform_analysis?: AdPerformancePlatformAnalysisDto[];

  @ApiPropertyOptional({ type: [AdPerformanceDepartmentAnalysisDto], description: 'Phân tích theo phòng ban' })
  department_analysis?: AdPerformanceDepartmentAnalysisDto[];

  @ApiPropertyOptional({ type: [AdPerformanceTopPerformerDto], description: 'Top chiến dịch hiệu quả nhất' })
  top_performers?: AdPerformanceTopPerformerDto[];

  @ApiPropertyOptional({ type: AdPerformanceComparisonDto, description: 'So sánh với kỳ trước' })
  comparison?: AdPerformanceComparisonDto;

  @ApiProperty({ description: 'Thời gian tạo báo cáo' })
  generated_at: Date;

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  report_period: string;

  @ApiProperty({ description: 'Bộ lọc áp dụng' })
  applied_filters: any;
} 