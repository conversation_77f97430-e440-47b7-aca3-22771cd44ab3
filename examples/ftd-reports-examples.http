### FTD (First Time Deposit) Reports API Examples

### Variables
@baseUrl = http://localhost:3000
@token = Bearer your-jwt-token-here

### 1. Get FTD Report - All Departments (Default: Last 30 days, Daily)
GET {{baseUrl}}/deposits/ftd-report
Authorization: {{token}}

### 2. Get FTD Report - Specific Date Range
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-01&endDate=2024-01-31
Authorization: {{token}}

### 3. Get FTD Report - Weekly Period
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-01&endDate=2024-01-31&period=weekly
Authorization: {{token}}

### 4. Get FTD Report - Monthly Period  
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-01&endDate=2024-12-31&period=monthly
Authorization: {{token}}

### 5. Get FTD Report - Specific Department
GET {{baseUrl}}/deposits/ftd-report?departmentId=1&startDate=2024-01-01&endDate=2024-01-31
Authorization: {{token}}

### 6. Get FTD Report - Filter by Department Name
GET {{baseUrl}}/deposits/ftd-report?departmentName=Sales&startDate=2024-01-01&endDate=2024-01-31
Authorization: {{token}}

### 7. Get FTD Report - With Pagination
GET {{baseUrl}}/deposits/ftd-report?page=1&limit=5&startDate=2024-01-01&endDate=2024-01-31
Authorization: {{token}}

### 8. Get FTD Report - Complete Example with All Parameters
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-01&endDate=2024-01-31&departmentId=1&period=daily&page=1&limit=10
Authorization: {{token}}

### 9. Get FTD Report - Yearly Analysis
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-01&endDate=2024-12-31&period=yearly
Authorization: {{token}}

### 10. Get FTD Report - Last 7 Days (Daily)
GET {{baseUrl}}/deposits/ftd-report?startDate=2024-01-25&endDate=2024-01-31&period=daily
Authorization: {{token}}

### Expected Response Structure:
### {
###   "summary": {
###     "totalFTDCount": 150,
###     "totalFTDAmount": 75000,
###     "averageFTDAmount": 500,
###     "totalNewUsers": 200,
###     "overallFTDRate": 75.0,
###     "growthRate": 12.5,
###     "totalDepartments": 5
###   },
###   "departmentStats": [
###     {
###       "departmentId": 1,
###       "departmentName": "Sales",
###       "totalFTDCount": 50,
###       "totalFTDAmount": 25000,
###       "averageFTDAmount": 500,
###       "totalUniqueUsers": 60,
###       "ftdRate": 83.33,
###       "createdAt": "2024-01-01T00:00:00.000Z",
###       "updatedAt": "2024-01-01T00:00:00.000Z"
###     }
###   ],
###   "timeSeries": [
###     {
###       "date": "2024-01-01",
###       "ftdCount": 5,
###       "ftdAmount": 2500,
###       "newUsersCount": 8,
###       "ftdRate": 62.5
###     }
###   ],
###   "topPerformers": [
###     {
###       "departmentId": 1,
###       "departmentName": "Sales",
###       "totalFTDAmount": 25000,
###       "ftdCount": 50,
###       "ftdRate": 83.33,
###       "rank": 1
###     }
###   ],
###   "dateRange": {
###     "startDate": "2024-01-01",
###     "endDate": "2024-01-31"
###   },
###   "period": "daily",
###   "pagination": {
###     "page": 1,
###     "limit": 10,
###     "total": 5,
###     "totalPages": 1
###   },
###   "generatedAt": "2024-01-31T12:00:00.000Z"
### } 