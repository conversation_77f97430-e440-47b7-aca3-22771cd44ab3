import { Entity, Column, <PERSON>T<PERSON><PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { User } from './user.entity';

export enum CostType {
  MARKETING = 'marketing',
  OPERATIONAL = 'operational',
  PERSONNEL = 'personnel',
  TECHNOLOGY = 'technology',
  OFFICE = 'office',
  TRAVEL = 'travel',
  TRAINING = 'training',
  OTHER = 'other'
}

export enum CostStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid'
}

@Entity('costs')
export class Cost extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tên chi phí'
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả chi phí'
  })
  description?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Số tiền chi phí'
  })
  amount: number;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'VND',
    comment: 'Đơn vị tiền tệ'
  })
  currency: string;

  @Column({
    type: 'enum',
    enum: CostType,
    default: CostType.OTHER,
    comment: 'Loại chi phí'
  })
  type: CostType;

  @Column({
    type: 'enum',
    enum: CostStatus,
    default: CostStatus.PENDING,
    comment: 'Trạng thái chi phí'
  })
  status: CostStatus;

  @Column({
    type: 'date',
    comment: 'Ngày phát sinh chi phí'
  })
  costDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Ngày thanh toán'
  })
  paymentDate?: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Nhà cung cấp/Vendor'
  })
  vendor?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Mã hóa đơn'
  })
  invoiceNumber?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Link Google Drive chứa chứng từ'
  })
  googleDriveLink?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Danh sách links Google Drive bổ sung'
  })
  additionalLinks?: string[];

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Tên file trong Google Drive'
  })
  fileName?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Loại file (pdf, xlsx, doc, etc.)'
  })
  fileType?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Ghi chú thêm'
  })
  notes?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Đã được duyệt'
  })
  isApproved: boolean;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Ngày duyệt'
  })
  approvedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Người duyệt'
  })
  approvedBy?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Lý do từ chối (nếu có)'
  })
  rejectionReason?: string;

  // Relationships
  @ManyToOne(() => Department, { nullable: false })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({
    type: 'uuid',
    comment: 'ID bộ phận'
  })
  department_id: string;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'created_by' })
  createdByUser: User;

  @Column({
    type: 'uuid',
    comment: 'Người tạo'
  })
  created_by: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approvedByUser?: User;

  // Computed properties
  get isOverdue(): boolean {
    if (!this.paymentDate && this.costDate) {
      const now = new Date();
      const costDate = new Date(this.costDate);
      const diffTime = now.getTime() - costDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays > 30; // Consider overdue after 30 days
    }
    return false;
  }

  get formattedAmount(): string {
    return `${this.amount.toLocaleString()} ${this.currency}`;
  }

  get hasDocuments(): boolean {
    return !!(this.googleDriveLink || (this.additionalLinks && this.additionalLinks.length > 0));
  }
} 