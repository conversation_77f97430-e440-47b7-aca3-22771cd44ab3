import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUI<PERSON>ipe,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AdPerformanceService } from './ad-performance.service';
import { CreateAdPerformanceDto } from './dto/create-ad-performance.dto';
import { UpdateAdPerformanceDto } from './dto/update-ad-performance.dto';
import { AdPerformanceQueryDto } from './dto/ad-performance-query.dto';
import { AdPerformanceResponseDto } from './dto/ad-performance-response.dto';
import { AdPerformanceReportQueryDto, AdPerformanceReportDto } from './dto/ad-performance-report.dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { Permissions } from '../../common/decorators/permissions.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { RoleType } from '../../common/constants/role.constant';
import { PermissionsType } from '../../common/constants/permissions.constant';

@ApiTags('Ad Performance')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@Controller('ad-performance')
export class AdPerformanceController {
  constructor(private readonly adPerformanceService: AdPerformanceService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.CREATE)
  @ApiOperation({ 
    summary: 'Tạo mới dữ liệu hiệu suất quảng cáo',
    description: 'Tạo mới một bản ghi hiệu suất quảng cáo với tính toán CTR, CPA, và các metrics khác'
  })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Dữ liệu hiệu suất quảng cáo đã được tạo thành công',
    type: AdPerformanceResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async create(
    @Body() createAdPerformanceDto: CreateAdPerformanceDto,
    @CurrentUser() user: any
  ): Promise<AdPerformanceResponseDto> {
    return this.adPerformanceService.create(createAdPerformanceDto, user.id);
  }

  @Get()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy danh sách hiệu suất quảng cáo',
    description: 'Lấy danh sách hiệu suất quảng cáo với phân trang, lọc và tìm kiếm'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Danh sách hiệu suất quảng cáo',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/AdPerformanceResponseDto' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async findAll(@Query() query: AdPerformanceQueryDto): Promise<{
    data: AdPerformanceResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return this.adPerformanceService.findAll(query);
  }

  @Get('report')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Tạo báo cáo hiệu suất quảng cáo',
    description: 'Tạo báo cáo chi tiết về hiệu suất quảng cáo với phân tích theo nền tảng, phòng ban, và time series'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Báo cáo hiệu suất quảng cáo',
    type: AdPerformanceReportDto
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async generateReport(@Query() query: AdPerformanceReportQueryDto): Promise<AdPerformanceReportDto> {
    return this.adPerformanceService.generateReport(query);
  }

  @Get(':id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  @ApiOperation({ 
    summary: 'Lấy chi tiết hiệu suất quảng cáo',
    description: 'Lấy thông tin chi tiết của một bản ghi hiệu suất quảng cáo'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chi tiết hiệu suất quảng cáo',
    type: AdPerformanceResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu hiệu suất quảng cáo' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<AdPerformanceResponseDto> {
    return this.adPerformanceService.findOne(id);
  }

  @Patch(':id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ 
    summary: 'Cập nhật hiệu suất quảng cáo',
    description: 'Cập nhật thông tin hiệu suất quảng cáo và tính toán lại các metrics'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Dữ liệu hiệu suất quảng cáo đã được cập nhật thành công',
    type: AdPerformanceResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Dữ liệu đầu vào không hợp lệ' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu hiệu suất quảng cáo' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAdPerformanceDto: UpdateAdPerformanceDto,
    @CurrentUser() user: any
  ): Promise<AdPerformanceResponseDto> {
    return this.adPerformanceService.update(id, updateAdPerformanceDto, user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ 
    summary: 'Xóa hiệu suất quảng cáo',
    description: 'Xóa một bản ghi hiệu suất quảng cáo khỏi hệ thống'
  })
  @ApiResponse({ 
    status: HttpStatus.NO_CONTENT, 
    description: 'Dữ liệu hiệu suất quảng cáo đã được xóa thành công' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu hiệu suất quảng cáo' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.adPerformanceService.remove(id);
  }

  @Patch(':id/recalculate-metrics')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ 
    summary: 'Tính toán lại các metrics',
    description: 'Tính toán lại CTR, CPA, ROAS, ROI và các metrics khác cho một chiến dịch quảng cáo'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Metrics đã được tính toán lại thành công',
    type: AdPerformanceResponseDto
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Không tìm thấy dữ liệu hiệu suất quảng cáo' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async recalculateMetrics(@Param('id', ParseUUIDPipe) id: string): Promise<AdPerformanceResponseDto> {
    return this.adPerformanceService.recalculateMetrics(id);
  }

  @Post('bulk-recalculate-metrics')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ 
    summary: 'Tính toán lại metrics hàng loạt',
    description: 'Tính toán lại CTR, CPA, ROAS, ROI cho nhiều chiến dịch quảng cáo cùng lúc'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Metrics đã được tính toán lại thành công',
    schema: {
      type: 'object',
      properties: {
        updated: { type: 'number', description: 'Số lượng bản ghi đã được cập nhật' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Không có quyền truy cập' 
  })
  @ApiResponse({ 
    status: HttpStatus.FORBIDDEN, 
    description: 'Không có quyền thực hiện hành động này' 
  })
  async bulkRecalculateMetrics(@Body() filters?: Partial<AdPerformanceQueryDto>): Promise<{ updated: number }> {
    return this.adPerformanceService.bulkRecalculateMetrics(filters);
  }
} 