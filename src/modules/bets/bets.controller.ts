import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  Res,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { BetsService } from './bets.service';
import { CreateBetDto } from './dto/create-bet.dto';
import { UpdateBetDto } from './dto/update-bet.dto';
import { BetChartDto } from './dto/bet-chart.dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { BetResponseDto } from './dto/bet-response.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { PermissionsType, RoleType } from '@/common/constants';
import { CurrentUser } from '@/common/decorators';
import { User } from '@/entities';

@ApiTags('bets')
@Controller('bets')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class BetsController {
  constructor(private readonly betsService: BetsService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo giao dịch cược mới' })
  @ApiResponse({ status: 201, description: 'Tạo thành công', type: BetResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.CREATE)
  create(@Body() createBetDto: CreateBetDto, @CurrentUser() user: User) {
    return this.betsService.create(createBetDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách giao dịch cược' })
  @ApiResponse({ status: 200, description: 'Lấy thành công' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findAll(@Query() paginationDto: PaginationDto) {
    return this.betsService.findAll(paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết giao dịch cược' })
  @ApiResponse({ status: 200, description: 'Lấy thành công', type: BetResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findOne(@Param('id') id: string) {
    return this.betsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật giao dịch cược' })
  @ApiResponse({ status: 200, description: 'Cập nhật thành công', type: BetResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.UPDATE)
  update(@Param('id') id: string, @Body() updateBetDto: UpdateBetDto) {
    return this.betsService.update(id, updateBetDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa giao dịch cược' })
  @ApiResponse({ status: 200, description: 'Xóa thành công' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.DELETE)
  remove(@Param('id') id: string) {
    return this.betsService.remove(id);
  }

  @Post('import')
  @ApiOperation({ summary: 'Import dữ liệu từ file Excel' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.IMPORT)
  async importFromExcel(@UploadedFile() file: any) {
    return this.betsService.importFromExcel(file);
  }

  @Get('export/excel')
  @ApiOperation({ summary: 'Export dữ liệu ra file Excel' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.EXPORT)
  async exportToExcel(@Res() res: Response) {
    const fileName = await this.betsService.exportToExcel();
    const filePath = `uploads/temp/${fileName}`;
    
    res.download(filePath, fileName, (err) => {
      if (err) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Error downloading file',
        });
      }
    });
  }

  @Get('chart/data')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ theo thời gian' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getChartData(@Query() chartDto: BetChartDto) {
    return this.betsService.getChartData(chartDto);
  }

  @Get('stats/department')
  @ApiOperation({ summary: 'Lấy thống kê theo bộ phận' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getDepartmentStats() {
    return this.betsService.getDepartmentStats();
  }
} 