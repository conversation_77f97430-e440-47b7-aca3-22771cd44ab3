import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { AssignMenuPermissionsDto } from './dto/assign-menu-permissions.dto';
import { PaginationDto, PaginatedResult } from '@/common/dto/pagination.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { CurrentUser } from '@/common/decorators/current-user.decorator';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { PermissionsType, RoleType } from '@/common/constants';
import { Permissions } from '@/common/decorators/permissions.decorator';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User created successfully',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Username or email already exists',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.UPDATE)
  async create(
    @Body() createUserDto: CreateUserDto,
    @CurrentUser() currentUser: any,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.create(createUserDto, currentUser.id);
    return plainToClass(UserResponseDto, user, { excludeExtraneousValues: true });
  }

  @Get()
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get all users with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<UserResponseDto>> {
    const result = await this.usersService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(user => 
        plainToClass(UserResponseDto, user, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.VIEW)
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<UserResponseDto> {
    const user = await this.usersService.findOne(id);
    return plainToClass(UserResponseDto, user, { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.UPDATE)
  @ApiOperation({ summary: 'Update user by ID' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User updated successfully',
    type: UserResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Username or email already exists',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
    @CurrentUser() currentUser: any,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.update(id, updateUserDto, currentUser.id);
    return plainToClass(UserResponseDto, user, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.DELETE)
  @ApiOperation({ summary: 'Delete user by ID' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'User deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: any,
  ): Promise<void> {
    await this.usersService.remove(id, currentUser.id);
  }

  @Post(':id/assign-role')
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Role assigned successfully',
    type: UserResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  async assignRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { roleId: string },
    @CurrentUser() currentUser: any,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.assignRole(id, body.roleId, currentUser.id);
    return plainToClass(UserResponseDto, user);
  }

  @Post(':id/assign-team')
  @ApiOperation({ summary: 'Assign team to user' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Team assigned successfully',
    type: UserResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  async assignTeam(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { teamId: string },
    @CurrentUser() currentUser: any,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.assignTeam(id, body.teamId, currentUser.id);
    return plainToClass(UserResponseDto, user);
  }

  @Post(':id/assign-department')
  @ApiOperation({ summary: 'Assign department to user' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Department assigned successfully',
    type: UserResponseDto,
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  async assignDepartment(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { departmentId: string },
    @CurrentUser() currentUser: any,
  ): Promise<UserResponseDto> {
    const user = await this.usersService.assignDepartment(id, body.departmentId, currentUser.id);
    return plainToClass(UserResponseDto, user);
  }

  @Post(':id/assign-menu-permissions')
  @ApiOperation({ summary: 'Assign menu permissions to user' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Menu permissions assigned successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER)
  async assignMenuPermissions(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignMenuPermissionsDto: AssignMenuPermissionsDto,
    @CurrentUser() currentUser: any,
  ): Promise<void> {
    await this.usersService.assignMenuPermissions(id, assignMenuPermissionsDto, currentUser.id);
  }

  @Get(':id/with-assignments')
  @ApiOperation({ summary: 'Get user with all assignments' })
  @ApiParam({ name: 'id', description: 'User ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User with assignments retrieved successfully',
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  async getUserWithAssignments(
    @Param('id', ParseUUIDPipe) id: string,
  ) {
    return this.usersService.getUserWithAssignments(id);
  }
}
