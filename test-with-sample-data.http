### Test User Menu Assignments with Sample Data
### Replace the UUIDs below with actual ones from your database

@baseUrl = http://localhost:3000/api/v1
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.sample-token

### Sample UUIDs - Replace these with real ones from your database
@sampleUserId = 11111111-1111-1111-1111-111111111111
@sampleMenuId = *************-2222-2222-************
@sampleSubmenuId = *************-3333-3333-************
@samplePermissionId1 = *************-4444-4444-************
@samplePermissionId2 = *************-5555-5555-************
@sampleRoleId = *************-6666-6666-************
@sampleDepartmentId = *************-7777-7777-************
@sampleTeamId = *************-8888-8888-************

### 1. Login to get real JWT token
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

###

### 2. Get real data from database to replace sample UUIDs above

### Get users
GET {{baseUrl}}/users?limit=3
Authorization: Bearer {{token}}

###

### Get menus
GET {{baseUrl}}/menus?limit=3
Authorization: Bearer {{token}}

###

### Get submenus
GET {{baseUrl}}/submenus?limit=3
Authorization: Bearer {{token}}

###

### Get permissions
GET {{baseUrl}}/permissions?limit=5
Authorization: Bearer {{token}}

###

### Get roles
GET {{baseUrl}}/roles?limit=3
Authorization: Bearer {{token}}

###

### 3. Test bulkAssign - Create menu assignment
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "menu": [
    {
      "id": "{{sampleMenuId}}",
      "permissionIds": [
        "{{samplePermissionId1}}",
        "{{samplePermissionId2}}"
      ]
    }
  ]
}

###

### 4. Test bulkAssign - Create submenu assignment
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "subMenu": [
    {
      "id": "{{sampleSubmenuId}}",
      "permissionIds": [
        "{{samplePermissionId1}}"
      ]
    }
  ]
}

###

### 5. Get created assignments to get their IDs
GET {{baseUrl}}/user-menu-assignments?userId={{sampleUserId}}
Authorization: Bearer {{token}}

###

### 6. Test updateBulkAssign - Update menu assignment
### Replace 'assignment-id-from-step-5' with actual ID from step 5
PATCH {{baseUrl}}/user-menu-assignments/assignment-id-from-step-5/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "menu": [
    {
      "id": "{{sampleMenuId}}",
      "permissionIds": [
        "{{samplePermissionId2}}"
      ]
    }
  ]
}

###

### 7. Test updateBulkAssign - Update submenu assignment
### Replace 'submenu-assignment-id-from-step-5' with actual ID from step 5
PATCH {{baseUrl}}/user-menu-assignments/submenu-assignment-id-from-step-5/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "subMenu": [
    {
      "id": "{{sampleSubmenuId}}",
      "permissionIds": [
        "{{samplePermissionId1}}",
        "{{samplePermissionId2}}"
      ]
    }
  ]
}

###

### 8. Test deleteBulk - Delete multiple assignments
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [
    "assignment-id-1-from-step-5",
    "assignment-id-2-from-step-5"
  ]
}

###

### 9. Verify assignments were deleted
GET {{baseUrl}}/user-menu-assignments?userId={{sampleUserId}}
Authorization: Bearer {{token}}

###

### Error Test Cases

### 10. Test updateBulkAssign with wrong assignment type (should fail)
### Try to update menu assignment with submenu data
PATCH {{baseUrl}}/user-menu-assignments/menu-assignment-id/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "subMenu": [
    {
      "id": "{{sampleSubmenuId}}",
      "permissionIds": ["{{samplePermissionId1}}"]
    }
  ]
}

###

### 11. Test updateBulkAssign with missing menu data (should fail)
PATCH {{baseUrl}}/user-menu-assignments/menu-assignment-id/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}"
}

###

### 12. Test updateBulkAssign with non-existent assignment (should return 404)
PATCH {{baseUrl}}/user-menu-assignments/00000000-0000-0000-0000-000000000000/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "{{sampleUserId}}",
  "roleId": "{{sampleRoleId}}",
  "menu": [
    {
      "id": "{{sampleMenuId}}",
      "permissionIds": ["{{samplePermissionId1}}"]
    }
  ]
}

###

### 13. Test bulkAssign with non-existent user (should fail)
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "00000000-0000-0000-0000-000000000000",
  "roleId": "{{sampleRoleId}}",
  "menu": [
    {
      "id": "{{sampleMenuId}}",
      "permissionIds": ["{{samplePermissionId1}}"]
    }
  ]
}

###

### 14. Test deleteBulk with empty array (should fail)
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": []
}
