import { <PERSON>ti<PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Department } from './department.entity';
import { BetStatus } from '../common/constants';

@Entity('bets')
export class Bet extends BaseEntity {
  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  bet_amount?: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  win_amount?: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  loss_amount?: number;

  @Column({ type: 'varchar', length: 50 })
  bet_id?: string;

  @Column({ type: 'varchar', length: 100 })
  game_type?: string;

  @Column({ type: 'varchar', length: 100 })
  game_name?: string;

  @Column({ type: 'varchar', length: 20, default: BetStatus.PENDING })
  status?: BetStatus;

  @Column({ type: 'timestamp', nullable: true })
  settled_at?: Date;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  external_reference?: string;

  @ManyToOne(() => Department, { nullable: false })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({ type: 'varchar' })
  department_id: string;
} 