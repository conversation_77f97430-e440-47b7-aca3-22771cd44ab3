# User Menu Assignments API Testing Guide

## Overview
This guide helps you test the `bulkAssign`, `updateBulkAssign`, and `deleteBulk` functions for User Menu Assignments.

## Files Created
1. **`test-user-menu-assignments.http`** - Main test file with comprehensive test cases
2. **`setup-test-data.http`** - Get real UUIDs from your database
3. **`debug-user-menu-assignments.http`** - Step-by-step debugging
4. **`test-with-sample-data.http`** - Tests with sample UUIDs (replace with real ones)
5. **`create-test-data.http`** - Create test data from scratch

## Testing Steps

### Step 1: Setup
1. Make sure server is running: `yarn start:dev` or `npm run start:dev`
2. Server should be available at: `http://localhost:3000`

### Step 2: Get Authentication Token
```http
POST http://localhost:3000/api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your-password"
}
```

### Step 3: Get Real UUIDs
Use `setup-test-data.http` to get real UUIDs from your database:
- User IDs
- Menu IDs  
- SubMenu IDs
- Permission IDs
- Role IDs
- Department IDs
- Team IDs

### Step 4: Test Functions

#### A. Test `bulkAssign` (Create)
```http
POST http://localhost:3000/api/v1/user-menu-assignments/bulk-assign
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "userId": "user-uuid",
  "roleId": "role-uuid",
  "menu": [
    {
      "id": "menu-uuid",
      "permissionIds": ["permission-uuid-1", "permission-uuid-2"]
    }
  ],
  "subMenu": [
    {
      "id": "submenu-uuid", 
      "permissionIds": ["permission-uuid-1"]
    }
  ]
}
```

#### B. Test `updateBulkAssign` (Update)
```http
PATCH http://localhost:3000/api/v1/user-menu-assignments/{assignment-id}/bulk-assign
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "userId": "user-uuid",
  "roleId": "role-uuid",
  "menu": [
    {
      "id": "menu-uuid",
      "permissionIds": ["permission-uuid-2"]
    }
  ]
}
```

#### C. Test `deleteBulk` (Delete)
```http
DELETE http://localhost:3000/api/v1/user-menu-assignments/bulk
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "ids": ["assignment-id-1", "assignment-id-2"]
}
```

## Expected Responses

### Success Responses
- **bulkAssign**: `201 Created` with array of `UserMenuAssignmentResponseDto`
- **updateBulkAssign**: `200 OK` with `UserMenuAssignmentResponseDto`
- **deleteBulk**: `204 No Content`

### Error Responses
- **400 Bad Request**: Invalid input data, validation errors
- **401 Unauthorized**: Missing or invalid JWT token
- **403 Forbidden**: User doesn't have required permissions (SUPER_ADMIN/MANAGER)
- **404 Not Found**: Assignment not found (for updateBulkAssign)
- **500 Internal Server Error**: Server error (check logs)

## Common Issues and Solutions

### 1. Authentication Issues
- **Problem**: 401 Unauthorized
- **Solution**: Make sure JWT token is valid and not expired

### 2. Authorization Issues  
- **Problem**: 403 Forbidden
- **Solution**: User must have SUPER_ADMIN or MANAGER role

### 3. Validation Errors
- **Problem**: 400 Bad Request with validation messages
- **Solutions**:
  - Ensure all UUIDs are in correct format
  - Provide required fields (userId, roleId)
  - Check that referenced entities exist

### 4. Assignment Not Found
- **Problem**: 404 Not Found for updateBulkAssign
- **Solution**: Use valid assignment ID from database

### 5. Server Errors
- **Problem**: 500 Internal Server Error
- **Solution**: Check server logs in terminal where you ran `yarn start:dev`

## Debugging Tips

1. **Check Server Logs**: Always monitor the terminal where server is running
2. **Verify Data**: Use GET endpoints to verify data exists before testing
3. **Test Step by Step**: Use `debug-user-menu-assignments.http` for systematic testing
4. **Check Database**: Verify data in database if needed

## Test Data Creation

If you need to create test data from scratch, use `create-test-data.http`:
1. Creates test user, department, team, role
2. Creates test menu, submenu, permissions
3. Provides cleanup scripts

## Validation Rules

### bulkAssign
- `userId` is required and must exist
- `roleId` is optional but must exist if provided
- At least one of `menu` or `subMenu` arrays must be provided
- All permission IDs must exist

### updateBulkAssign
- Assignment ID must exist
- For menu-level assignments: must provide `menu` array
- For submenu-level assignments: must provide `subMenu` array
- Menu/submenu ID in request must match the assignment's menu/submenu

### deleteBulk
- `ids` array cannot be empty
- All assignment IDs must exist

## Security Notes
- All endpoints require JWT authentication
- Only SUPER_ADMIN and MANAGER roles can access these endpoints
- All operations are logged with the user who performed them
- Soft delete is used (data is not permanently removed)
