import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerModule } from './common/logger/logger.module';
import { RedisModule } from './common/redis/redis.module';
import { QueueModule } from './common/queue/queue.module';
import configuration from './config/configuration';

// Import all modules
import { UsersModule } from './modules/users/users.module';
import { DepartmentsModule } from './modules/departments/departments.module';
import { TeamsModule } from './modules/teams/teams.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { RolesModule } from './modules/roles/roles.module';
import { UserMenuAssignmentsModule } from './modules/user-menu-assignments/user-menu-assignments.module';
import { AuthModule } from './modules/auth/auth.module';
import { UploadModule } from './modules/upload/upload.module';
import { AffiliateModule } from './modules/affiliate/affiliate.module';
import { AuditModule } from './modules/audit/audit.module';
import { DepositsModule } from './modules/deposits/deposits.module';
import { BetsModule } from './modules/bets/bets.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { CostsModule } from './modules/costs/costs.module';
import { AdPerformanceModule } from './modules/ad-performance/ad-performance.module';
import { DataManagementModule } from './modules/data-management/data-management.module';
import { QueueMonitorModule } from './modules/queue-monitor/queue-monitor.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('database.synchronize'),
        logging: configService.get('database.logging'),
        maxQueryExecutionTime: 1000,
        extra: {},
      }),
      inject: [ConfigService],
    }),
    LoggerModule,
    RedisModule,
    QueueModule,
    // RBAC Modules
    UsersModule,
    DepartmentsModule,
    TeamsModule,
    PermissionsModule,
    RolesModule,
    UserMenuAssignmentsModule,
    AuthModule,
    UploadModule,
    AffiliateModule,
    AuditModule,
    DepositsModule,
    BetsModule,
    DashboardModule,
    CostsModule,
    AdPerformanceModule,
    DataManagementModule,
    QueueMonitorModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
