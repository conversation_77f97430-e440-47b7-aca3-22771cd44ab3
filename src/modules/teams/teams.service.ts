import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Team } from '@/entities/team.entity';
import { Department } from '@/entities/department.entity';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { UserContextService, UserContext } from '@/common/services/user-context.service';
import { CustomLoggerService } from '@/common/logger/logger.service';

@Injectable()
export class TeamsService {
  constructor(
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    private readonly userContextService: UserContextService,
    private readonly logger: CustomLoggerService,
  ) {}

  async create(createTeamDto: CreateTeamDto, createdBy?: string, currentUserId?: string): Promise<Team> {
    this.logger.info(
      `Creating new team: ${createTeamDto.name} in department: ${createTeamDto.departmentId}`,
      'TeamsService',
      { name: createTeamDto.name, departmentId: createTeamDto.departmentId, createdBy }
    );

    // Get current user context for permission checking
    let userContext: UserContext | null = null;
    if (currentUserId) {
      userContext = await this.userContextService.getUserContext(currentUserId);
    }

    // Check if department exists
    const department = await this.departmentRepository.findOne({
      where: { id: createTeamDto.departmentId },
    });
    if (!department) {
      throw new BadRequestException('Department not found');
    }

    // Check if current user can create team in this department
    if (userContext && !this.userContextService.canManageDepartment(userContext, createTeamDto.departmentId)) {
      throw new ForbiddenException('You can only create teams within your own department');
    }

    // Check if team name already exists in the same department
    const existingTeam = await this.teamRepository.findOne({
      where: { 
        name: createTeamDto.name,
        departmentId: createTeamDto.departmentId,
      },
    });
    if (existingTeam) {
      throw new ConflictException('Team name already exists in this department');
    }

    // Create team
    const team = this.teamRepository.create({
      ...createTeamDto,
      createdBy,
    });

    return await this.teamRepository.save(team);
  }

  async findAll(paginationDto: PaginationDto, departmentId?: string): Promise<PaginatedResult<Team>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.teamRepository.createQueryBuilder('team')
      .leftJoinAndSelect('team.department', 'department');
    
    // Filter by department if provided
    if (departmentId) {
      queryBuilder.where('team.departmentId = :departmentId', { departmentId });
    }
    
    // Add search functionality
    if (search) {
      const searchCondition = departmentId 
        ? 'team.name LIKE :search'
        : '(team.name LIKE :search OR department.name LIKE :search)';
      
      if (departmentId) {
        queryBuilder.andWhere(searchCondition, { search: `%${search}%` });
      } else {
        queryBuilder.andWhere(searchCondition, { search: `%${search}%` });
      }
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`team.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [teams, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(teams, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Team> {
    const team = await this.teamRepository.findOne({
      where: { id },
      relations: ['department', 'users'],
    });

    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return team;
  }

  async findByDepartment(departmentId: string): Promise<Team[]> {
    return await this.teamRepository.find({
      where: { departmentId },
      relations: ['department'],
      order: { name: 'ASC' },
    });
  }

  async update(id: string, updateTeamDto: UpdateTeamDto, updatedBy?: string): Promise<Team> {
    const team = await this.findOne(id);

    // If department is being updated, check if it exists
    if (updateTeamDto.departmentId && updateTeamDto.departmentId !== team.departmentId) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateTeamDto.departmentId },
      });
      if (!department) {
        throw new BadRequestException('Department not found');
      }
    }

    // Check if name is being updated and already exists in the target department
    if (updateTeamDto.name && updateTeamDto.name !== team.name) {
      const targetDepartmentId = updateTeamDto.departmentId || team.departmentId;
      const existingTeam = await this.teamRepository.findOne({
        where: { 
          name: updateTeamDto.name,
          departmentId: targetDepartmentId,
        },
      });
      if (existingTeam) {
        throw new ConflictException('Team name already exists in this department');
      }
    }

    // Update team
    Object.assign(team, updateTeamDto, { updatedBy });
    return await this.teamRepository.save(team);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const team = await this.findOne(id);
    
    // Check if team has users
    if (team.users && team.users.length > 0) {
      throw new ConflictException('Cannot delete team with existing users');
    }
    
    // Soft delete
    team.deletedBy = deletedBy;
    await this.teamRepository.softRemove(team);
  }

  async getTeamStats(id: string): Promise<{
    totalUsers: number;
    departmentName: string;
  }> {
    const team = await this.teamRepository
      .createQueryBuilder('team')
      .leftJoinAndSelect('team.department', 'department')
      .leftJoinAndSelect('team.users', 'users')
      .where('team.id = :id', { id })
      .getOne();

    if (!team) {
      throw new NotFoundException(`Team with ID ${id} not found`);
    }

    return {
      totalUsers: team.users?.length || 0,
      departmentName: team.department?.name || 'Unknown',
    };
  }
}
