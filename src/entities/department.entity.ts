import { <PERSON>tity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Team } from './team.entity';
import { User } from './user.entity';

/**
 * Department Entity
 * Represents departments in the organization
 */
@Entity('departments')
export class Department extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Name of the department',
  })
  name: string;

  // Relationships
  @OneToMany(() => Team, (team) => team.department)
  teams: Team[];

  @OneToMany(() => User, (user) => user.department)
  users: User[];
}
