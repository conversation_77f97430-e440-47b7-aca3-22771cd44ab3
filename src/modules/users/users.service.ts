import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User } from '@/entities/user.entity';
import { Role } from '@/entities/role.entity';
import { Team } from '@/entities/team.entity';
import { Department } from '@/entities/department.entity';
import { UserMenuAssignment } from '@/entities/user-menu-assignment.entity';
import { Menu } from '@/entities/menu.entity';
import { SubMenu } from '@/entities/submenu.entity';
import { Permission } from '@/entities/permission.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PaginationDto, PaginatedResult, createPaginatedResult } from '@/common/dto/pagination.dto';
import { AffiliateService } from './services/affiliate.service';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { UserRoleAssignment } from '@/entities/user-role-assignment.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Team)
    private readonly teamRepository: Repository<Team>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(UserMenuAssignment)
    private readonly userMenuAssignmentRepository: Repository<UserMenuAssignment>,
    @InjectRepository(Menu)
    private readonly menuRepository: Repository<Menu>,
    @InjectRepository(SubMenu)
    private readonly submenuRepository: Repository<SubMenu>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    private readonly affiliateService: AffiliateService,
    private readonly logger: CustomLoggerService,
    @InjectRepository(UserRoleAssignment)
    private readonly userRoleAssignmentRepository: Repository<UserRoleAssignment>,
  ) {}

  async create(createUserDto: CreateUserDto, createdBy?: string): Promise<User> {
    this.logger.info(
      `Creating new user: ${createUserDto.username}`,
      'UsersService',
      { username: createUserDto.username, email: createUserDto.email, createdBy }
    );

    // Check if username already exists
    const existingUsername = await this.userRepository.findOne({
      where: { username: createUserDto.username },
    });
    if (existingUsername) {
      this.logger.warn(
        `User creation failed - username already exists: ${createUserDto.username}`,
        'UsersService'
      );
      throw new ConflictException('Username already exists');
    }

    // Check if email already exists
    const existingEmail = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });
    if (existingEmail) {
      this.logger.warn(
        `User creation failed - email already exists: ${createUserDto.email}`,
        'UsersService'
      );
      throw new ConflictException('Email already exists');
    }

    try {
      // Validate role if provided
      if (createUserDto.roleId) {
        const role = await this.roleRepository.findOne({ where: { id: createUserDto.roleId } });
        if (!role) {
          throw new BadRequestException('Role not found');
        }
      }

      // Validate team if provided
      if (createUserDto.teamId) {
        const team = await this.teamRepository.findOne({ where: { id: createUserDto.teamId } });
        if (!team) {
          throw new BadRequestException('Team not found');
        }
      }

      // Validate department if provided
      if (createUserDto.departmentId) {
        const department = await this.departmentRepository.findOne({ where: { id: createUserDto.departmentId } });
        if (!department) {
          throw new BadRequestException('Department not found');
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

      // Create user
      const { menuAssignments, ...userDto } = createUserDto;
      const user = this.userRepository.create({
        ...userDto,
        password: hashedPassword,
        createdBy,
      });

      const savedUser = await this.userRepository.save(user);

      // Process menu assignments if provided
      if (menuAssignments && (menuAssignments.menu?.length > 0 || menuAssignments.subMenu?.length > 0)) {
        await this.processMenuAssignments(savedUser.id, menuAssignments, createdBy);
      }

      // Process referral if referredBy is provided
      if (createUserDto.referredBy) {
        try {
          await this.userRepository.update(savedUser.id, {
            affiliates: [{ id: createUserDto.referredBy, isActive: true }],
          });
          this.logger.info(
            `Referral processed for user: ${savedUser.username}`,
            'UsersService',
            { userId: savedUser.id, referredBy: createUserDto.referredBy }
          );
        } catch (error) {
          this.logger.error(
            `Failed to set referrer for user: ${savedUser.username}`,
            error.stack,
            'UsersService'
          );
        }
      }

      if (savedUser.id) {
        // Process role assignments
        if (createUserDto.roleId) {
          const role = await this.roleRepository.findOne({ where: { id: createUserDto.roleId } });
          if (!role) {
            throw new BadRequestException('Role not found');
          }
        }

        // Validate department exists
        if (createUserDto.departmentId) {
          const department = await this.departmentRepository.findOne({ where: { id: createUserDto.departmentId } });
          if (!department) {
            throw new BadRequestException('Department not found');
          }
        }

        // Validate team exists
        if (createUserDto.teamId) {
          const team = await this.teamRepository.findOne({ where: { id: createUserDto.teamId } });
          if (!team) {
            throw new BadRequestException('Team not found');
          }
        }

        const whereConditionRole: {
          userId: string;
          roleId?: string;
          departmentId?: string;
          teamId?: string;
        } = {
          userId: savedUser.id,
          roleId: createUserDto?.roleId,
        };

        if (createUserDto.departmentId) {
          whereConditionRole.departmentId = createUserDto.departmentId;
        }
        
        if (createUserDto.teamId) {
          whereConditionRole.teamId = createUserDto.teamId;
        }
        
        // Check if role assignment already exists
        const existingRoleAssignment = await this.userRoleAssignmentRepository.findOne({
          where: whereConditionRole,
        });

        // Create user role assignment
        const userRoleAssignment = this.userRoleAssignmentRepository.create({
          userId: savedUser.id,
          roleId: createUserDto.roleId,
          departmentId: createUserDto.departmentId,
          teamId: createUserDto.teamId,
          createdBy,
        });

        if (existingRoleAssignment) {
          throw new ConflictException('User role assignment already exists');
        }

        await this.userRoleAssignmentRepository.save(userRoleAssignment);
        
      }

      this.logger.info(
        `User created successfully: ${savedUser.username}`,
        'UsersService',
        { userId: savedUser.id, username: savedUser.username }
      );

      return savedUser;
    } catch (error) {
      this.logger.error(
        `Failed to create user: ${createUserDto.username}`,
        error.stack,
        'UsersService'
      );
      throw error;
    }
  }

  async findAll(paginationDto: PaginationDto): Promise<PaginatedResult<User>> {
    const { page, limit, search, sortBy, sortOrder } = paginationDto;
    
    const queryBuilder = this.userRepository.createQueryBuilder('user');
    
    // Add search functionality
    if (search) {
      queryBuilder.where(
        '(user.username LIKE :search OR user.fullName LIKE :search OR user.email LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Add sorting
    const sortField = sortBy || 'createdAt';
    const sortDirection = sortOrder || 'DESC';
    queryBuilder.orderBy(`user.${sortField}`, sortDirection);

    // Add pagination
    const skip = ((page || 1) - 1) * (limit || 10);
    queryBuilder.skip(skip).take(limit || 10);

    // Execute query
    const [users, total] = await queryBuilder.getManyAndCount();

    return createPaginatedResult(users, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<User> {
    this.logger.debug(`Finding user by ID: ${id}`, 'UsersService');

    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['role', 'team', 'department'],
    });

    if (!user) {
      this.logger.warn(`User not found: ${id}`, 'UsersService');
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    this.logger.debug(`User found: ${user.username}`, 'UsersService');
    return user;
  }

  async findByUsername(username: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { username },
      relations: ['userRoles', 'userRoles.role'],
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email },
      relations: ['userRoles', 'userRoles.role'],
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto, updatedBy?: string): Promise<User> {
    const user = await this.findOne(id);

    // Check if username is being updated and already exists
    if (updateUserDto.username && updateUserDto.username !== user.username) {
      const existingUsername = await this.userRepository.findOne({
        where: { username: updateUserDto.username },
      });
      if (existingUsername) {
        throw new ConflictException('Username already exists');
      }
    }

    // Check if email is being updated and already exists
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });
      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }
    }

    // Update user
    Object.assign(user, updateUserDto, { updatedBy });
    return await this.userRepository.save(user);
  }

  async remove(id: string, deletedBy?: string): Promise<void> {
    const user = await this.findOne(id);
    
    // Soft delete
    user.deletedBy = deletedBy;
    await this.userRepository.softRemove(user);
  }

  async changePassword(id: string, newPassword: string, updatedBy?: string): Promise<void> {
    const user = await this.findOne(id);
    
    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update password
    user.password = hashedPassword;
    user.updatedBy = updatedBy;
    
    await this.userRepository.save(user);
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return await bcrypt.compare(password, user.password);
  }

  /**
   * Process menu assignments for a user
   */
  private async processMenuAssignments(
    userId: string,
    menuAssignments: {
      menu?: { id: string; permissionIds: string[] }[];
      subMenu?: { id: string; permissionIds: string[] }[];
    },
    createdBy?: string
  ): Promise<void> {
    // Process main menu assignments
    if (menuAssignments.menu) {
      for (const menuAssignment of menuAssignments.menu) {
        // Validate menu exists
        const menu = await this.menuRepository.findOne({ where: { id: menuAssignment.id } });
        if (!menu) {
          this.logger.warn(`Menu not found: ${menuAssignment.id}`, 'UsersService');
          continue;
        }

        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(menuAssignment.permissionIds) }
        });
        if (permissions.length !== menuAssignment.permissionIds.length) {
          this.logger.warn(`Some permissions not found for menu assignment: ${menuAssignment.id}`, 'UsersService');
          continue;
        }

        // Create menu assignment
        const userMenuAssignment = this.userMenuAssignmentRepository.create({
          userId,
          menuId: menuAssignment.id,
          permissions,
          createdBy,
        });

        await this.userMenuAssignmentRepository.save(userMenuAssignment);
      }
    }

    // Process submenu assignments
    if (menuAssignments.subMenu) {
      for (const submenuAssignment of menuAssignments.subMenu) {
        // Validate submenu exists and get its menu
        const submenu = await this.submenuRepository.findOne({
          where: { id: submenuAssignment.id },
          relations: ['menu']
        });
        if (!submenu) {
          this.logger.warn(`SubMenu not found: ${submenuAssignment.id}`, 'UsersService');
          continue;
        }

        // Validate permissions exist
        const permissions = await this.permissionRepository.find({
          where: { id: In(submenuAssignment.permissionIds) }
        });
        if (permissions.length !== submenuAssignment.permissionIds.length) {
          this.logger.warn(`Some permissions not found for submenu assignment: ${submenuAssignment.id}`, 'UsersService');
          continue;
        }

        // Create submenu assignment
        const userMenuAssignment = this.userMenuAssignmentRepository.create({
          userId,
          menuId: submenu.menu.id,
          submenuId: submenuAssignment.id,
          permissions,
          createdBy,
        });

        await this.userMenuAssignmentRepository.save(userMenuAssignment);
      }
    }
  }

  /**
   * Assign role to user
   */
  async assignRole(userId: string, roleId: string, updatedBy?: string): Promise<User> {
    const user = await this.findOne(userId);
    const role = await this.roleRepository.findOne({ where: { id: roleId } });

    if (!role) {
      throw new BadRequestException('Role not found');
    }

    user.roleId = roleId;
    user.updatedBy = updatedBy;

    return this.userRepository.save(user);
  }

  /**
   * Assign team to user
   */
  async assignTeam(userId: string, teamId: string, updatedBy?: string): Promise<User> {
    const user = await this.findOne(userId);
    const team = await this.teamRepository.findOne({ where: { id: teamId } });

    if (!team) {
      throw new BadRequestException('Team not found');
    }

    user.teamId = teamId;
    user.updatedBy = updatedBy;

    return this.userRepository.save(user);
  }

  /**
   * Assign department to user
   */
  async assignDepartment(userId: string, departmentId: string, updatedBy?: string): Promise<User> {
    const user = await this.findOne(userId);
    const department = await this.departmentRepository.findOne({ where: { id: departmentId } });

    if (!department) {
      throw new BadRequestException('Department not found');
    }

    user.departmentId = departmentId;
    user.updatedBy = updatedBy;

    return this.userRepository.save(user);
  }

  /**
   * Assign menu permissions to user
   */
  async assignMenuPermissions(
    userId: string,
    menuAssignments: {
      menu?: { id: string; permissionIds: string[] }[];
      subMenu?: { id: string; permissionIds: string[] }[];
    },
    updatedBy?: string
  ): Promise<void> {
    const user = await this.findOne(userId);

    // Remove existing assignments for this user
    await this.userMenuAssignmentRepository.delete({ userId });

    // Create new assignments
    await this.processMenuAssignments(userId, menuAssignments, updatedBy);
  }

  /**
   * Get user with all assignments
   */
  async getUserWithAssignments(userId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: [
        'role',
        'team',
        'department',
        'menuAssignments',
        'menuAssignments.menu',
        'menuAssignments.submenu',
        'menuAssignments.permissions'
      ],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return user;
  }
}
