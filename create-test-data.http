### Create Test Data for User Menu Assignments Testing
### Run these requests in order to create test data

@baseUrl = http://localhost:3000/api/v1
@token = your-jwt-token-here

### 1. Login first
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

###

### 2. Create test user
POST {{baseUrl}}/users
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "username": "testuser_menu",
  "email": "<EMAIL>",
  "fullName": "Test User for Menu Assignments",
  "password": "testpass123",
  "isActive": true
}

###

### 3. Create test department
POST {{baseUrl}}/departments
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Test Department Menu",
  "description": "Department for menu assignment testing",
  "isActive": true
}

###

### 4. Create test team (use department ID from step 3)
POST {{baseUrl}}/teams
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Test Team Menu",
  "description": "Team for menu assignment testing",
  "departmentId": "department-id-from-step-3",
  "isActive": true
}

###

### 5. Create test role
POST {{baseUrl}}/roles
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_MENU_ROLE",
  "description": "Role for menu assignment testing",
  "isActive": true
}

###

### 6. Create test menu
POST {{baseUrl}}/menus
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Test Menu for Assignments",
  "description": "Menu for testing assignments",
  "icon": "test-menu-icon",
  "order": 999,
  "isActive": true
}

###

### 7. Create test submenu (use menu ID from step 6)
POST {{baseUrl}}/submenus
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "title": "Test SubMenu for Assignments",
  "description": "SubMenu for testing assignments",
  "link": "/test-submenu-assignments",
  "icon": "test-submenu-icon",
  "order": 1,
  "menuId": "menu-id-from-step-6",
  "isActive": true
}

###

### 8. Create test permissions
POST {{baseUrl}}/permissions
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_MENU_READ",
  "action": "read",
  "description": "Test read permission for menu assignments"
}

###

POST {{baseUrl}}/permissions
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_MENU_WRITE",
  "action": "write",
  "description": "Test write permission for menu assignments"
}

###

POST {{baseUrl}}/permissions
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "TEST_MENU_DELETE",
  "action": "delete",
  "description": "Test delete permission for menu assignments"
}

###

### 9. Verify all test data was created
GET {{baseUrl}}/users?search=testuser_menu
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/departments?search=Test Department Menu
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/teams?search=Test Team Menu
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/roles?search=TEST_MENU_ROLE
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/menus?search=Test Menu for Assignments
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/submenus?search=Test SubMenu for Assignments
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/permissions?search=TEST_MENU
Authorization: Bearer {{token}}

###

### 10. Now you can copy the IDs from the responses above and use them in your tests

### Sample test with the created data (replace IDs with actual ones):
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "user-id-from-step-2",
  "departmentId": "department-id-from-step-3",
  "teamId": "team-id-from-step-4",
  "roleId": "role-id-from-step-5",
  "menu": [
    {
      "id": "menu-id-from-step-6",
      "permissionIds": [
        "permission-id-from-step-8-read",
        "permission-id-from-step-8-write"
      ]
    }
  ],
  "subMenu": [
    {
      "id": "submenu-id-from-step-7",
      "permissionIds": [
        "permission-id-from-step-8-read"
      ]
    }
  ]
}

###

### Clean up test data (run after testing)
### Delete test assignments first
GET {{baseUrl}}/user-menu-assignments?userId=user-id-from-step-2
Authorization: Bearer {{token}}

###

### Delete the assignments using their IDs
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [
    "assignment-id-1",
    "assignment-id-2"
  ]
}

###

### Delete test data (in reverse order)
DELETE {{baseUrl}}/permissions/permission-id-from-step-8-delete
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/permissions/permission-id-from-step-8-write
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/permissions/permission-id-from-step-8-read
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/submenus/submenu-id-from-step-7
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/menus/menu-id-from-step-6
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/roles/role-id-from-step-5
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/teams/team-id-from-step-4
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/departments/department-id-from-step-3
Authorization: Bearer {{token}}

###

DELETE {{baseUrl}}/users/user-id-from-step-2
Authorization: Bearer {{token}}
