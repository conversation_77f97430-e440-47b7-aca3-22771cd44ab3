import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { DepartmentsService } from './departments.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { DepartmentResponseDto } from './dto/department-response.dto';
import { PaginationDto, PaginatedResult } from '../../common/dto/pagination.dto';
import { Roles } from '@/common/decorators';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { PermissionsType, RoleType } from '@/common/constants';
import { JwtAuthGuard, PermissionsGuard, RolesGuard } from '@/common/guards';

@ApiTags('Departments')
@Controller('departments')
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new department' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Department created successfully',
    type: DepartmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Department name already exists',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.CREATE)
  async create(@Body() createDepartmentDto: CreateDepartmentDto): Promise<DepartmentResponseDto> {
    const department = await this.departmentsService.create(createDepartmentDto, 'system');
    return plainToClass(DepartmentResponseDto, department, { excludeExtraneousValues: true });
  }

  @Get()
  @ApiOperation({ summary: 'Get all departments with pagination and search' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Departments retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order' })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findAll(@Query() paginationDto: PaginationDto): Promise<PaginatedResult<DepartmentResponseDto>> {
    const result = await this.departmentsService.findAll(paginationDto);
    
    return {
      ...result,
      data: result.data.map(department => 
        plainToClass(DepartmentResponseDto, department, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Department retrieved successfully',
    type: DepartmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Department not found',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentsService.findOne(id);
    return plainToClass(DepartmentResponseDto, department, { excludeExtraneousValues: true });
  }

  @Get(':id/stats')
  @ApiOperation({ summary: 'Get department statistics' })
  @ApiParam({ name: 'id', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Department statistics retrieved successfully',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.READ)
  async getStats(@Param('id', ParseUUIDPipe) id: string) {
    return await this.departmentsService.getDepartmentStats(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Department updated successfully',
    type: DepartmentResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Department name already exists',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.UPDATE)
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    const department = await this.departmentsService.update(id, updateDepartmentDto, 'system');
    return plainToClass(DepartmentResponseDto, department, { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Department deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Department not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Cannot delete department with existing teams',
  })
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles(RoleType.SUPER_ADMIN)
  @Permissions(PermissionsType.DELETE)
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.departmentsService.remove(id, 'system');
  }
}
