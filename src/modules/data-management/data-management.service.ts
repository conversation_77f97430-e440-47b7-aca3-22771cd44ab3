import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserAnalytics, RegistrationSource, MemberStatus, ActivityLevel, GeographicRegion } from '../../entities/user-analytics.entity';
import { User } from '../../entities/user.entity';
import { Department } from '../../entities/department.entity';
import { Deposit } from '../../entities/deposit.entity';
import { Bet } from '../../entities/bet.entity';
import { CreateUserAnalyticsDto } from './dto/create-user-analytics.dto';
import { UpdateUserAnalyticsDto } from './dto/update-user-analytics.dto';
import { UserAnalyticsQueryDto } from './dto/user-analytics-query.dto';
import { UserAnalyticsResponseDto } from './dto/user-analytics-response.dto';
import { 
  DataManagementReportQueryDto,
  DataManagementReportDto,
  RegistrationStatsDto,
  RegistrationTimeSeriesDto,
  RegistrationSourceAnalysisDto,
  MemberAnalyticsStatsDto,
  MemberStatusDistributionDto,
  MemberActivityLevelDto,
  FTDAnalysisStatsDto,
  FTDTimeSeriesDto,
  FTDSourceAnalysisDto,
  GeographicAnalysisDto,
  DepartmentAnalysisDto,
  TopPerformerDto,
  DataManagementComparisonDto
} from './dto/data-management-report.dto';
import { plainToClass } from 'class-transformer';
import { UserContextService } from '../../common/services/user-context.service';

@Injectable()
export class DataManagementService {
  constructor(
    @InjectRepository(UserAnalytics)
    private readonly userAnalyticsRepository: Repository<UserAnalytics>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Deposit)
    private readonly depositRepository: Repository<Deposit>,
    @InjectRepository(Bet)
    private readonly betRepository: Repository<Bet>,
    private readonly userContextService: UserContextService,
  ) {}

  // CRUD Operations for UserAnalytics
  async createUserAnalytics(createDto: CreateUserAnalyticsDto): Promise<UserAnalyticsResponseDto> {
    // Validate user exists
    const user = await this.userRepository.findOne({
      where: { id: createDto.user_id }
    });
    
    if (!user) {
      throw new NotFoundException(`User with ID ${createDto.user_id} not found`);
    }

    // Check if analytics already exist for this user
    const existingAnalytics = await this.userAnalyticsRepository.findOne({
      where: { user_id: createDto.user_id }
    });

    if (existingAnalytics) {
      throw new BadRequestException(`Analytics already exist for user ${createDto.user_id}`);
    }

    // Validate department if provided
    if (createDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: createDto.department_id }
      });
      
      if (!department) {
        throw new NotFoundException(`Department with ID ${createDto.department_id} not found`);
      }
    }

    // Create analytics record
    const analytics = new UserAnalytics();
    Object.assign(analytics, {
      ...createDto,
      registration_date: new Date(createDto.registration_date),
      last_login_date: createDto.last_login_date ? new Date(createDto.last_login_date) : null,
      first_deposit_date: createDto.first_deposit_date ? new Date(createDto.first_deposit_date) : null,
      last_deposit_date: createDto.last_deposit_date ? new Date(createDto.last_deposit_date) : null,
      kyc_verified_date: createDto.kyc_verified_date ? new Date(createDto.kyc_verified_date) : null,
    });

    // Calculate initial metrics
    analytics.calculateChurnProbability();
    analytics.updateMemberStatus();

    const savedAnalytics = await this.userAnalyticsRepository.save(analytics);
    
    return this.transformToResponseDto(savedAnalytics);
  }

  async findAllUserAnalytics(query: UserAnalyticsQueryDto): Promise<{
    data: UserAnalyticsResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 10, ...filters } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createAnalyticsQueryBuilder(filters);
    
    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const transformedData = data.map(item => this.transformToResponseDto(item));

    return {
      data: transformedData,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOneUserAnalytics(id: string): Promise<UserAnalyticsResponseDto> {
    const analytics = await this.userAnalyticsRepository.findOne({
      where: { id },
      relations: ['user', 'department', 'referred_by_user'],
    });

    if (!analytics) {
      throw new NotFoundException(`User Analytics with ID ${id} not found`);
    }

    return this.transformToResponseDto(analytics);
  }

  async updateUserAnalytics(id: string, updateDto: UpdateUserAnalyticsDto): Promise<UserAnalyticsResponseDto> {
    const analytics = await this.userAnalyticsRepository.findOne({
      where: { id },
      relations: ['user', 'department', 'referred_by_user'],
    });

    if (!analytics) {
      throw new NotFoundException(`User Analytics with ID ${id} not found`);
    }

    // Validate department if provided
    if (updateDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateDto.department_id }
      });
      
      if (!department) {
        throw new NotFoundException(`Department with ID ${updateDto.department_id} not found`);
      }
    }

    // Update the record
    Object.assign(analytics, {
      ...updateDto,
      ...(updateDto.registration_date && { registration_date: new Date(updateDto.registration_date) }),
      ...(updateDto.last_login_date && { last_login_date: new Date(updateDto.last_login_date) }),
      ...(updateDto.first_deposit_date && { first_deposit_date: new Date(updateDto.first_deposit_date) }),
      ...(updateDto.last_deposit_date && { last_deposit_date: new Date(updateDto.last_deposit_date) }),
      ...(updateDto.kyc_verified_date && { kyc_verified_date: new Date(updateDto.kyc_verified_date) }),
    });

    // Recalculate metrics
    analytics.calculateChurnProbability();
    analytics.updateMemberStatus();

    const updatedAnalytics = await this.userAnalyticsRepository.save(analytics);
    
    return this.transformToResponseDto(updatedAnalytics);
  }

  async removeUserAnalytics(id: string): Promise<void> {
    const analytics = await this.userAnalyticsRepository.findOne({
      where: { id },
    });

    if (!analytics) {
      throw new NotFoundException(`User Analytics with ID ${id} not found`);
    }

    await this.userAnalyticsRepository.remove(analytics);
  }

  // Comprehensive Report Generation
  async generateReport(query: DataManagementReportQueryDto): Promise<DataManagementReportDto> {
    const baseQuery = this.userAnalyticsRepository.createQueryBuilder('analytics')
      .leftJoinAndSelect('analytics.user', 'user')
      .leftJoinAndSelect('analytics.department', 'department');

    // Apply filters
    this.applyReportFilters(baseQuery, query);

    const report: DataManagementReportDto = {
      generated_at: new Date(),
      report_period: this.formatReportPeriod(query.start_date || '', query.end_date || ''),
      report_type: query.report_type || 'general',
      applied_filters: query,
    };

    // Generate different sections based on report type
    switch (query.report_type) {
      case 'registration':
        await this.addRegistrationData(report, baseQuery, query);
        break;
      case 'member_analytics':
        await this.addMemberAnalyticsData(report, baseQuery, query);
        break;
      case 'ftd_analysis':
        await this.addFTDAnalysisData(report, baseQuery, query);
        break;
      case 'lifecycle':
        await this.addLifecycleData(report, baseQuery, query);
        break;
      case 'comprehensive':
      default:
        await this.addAllReportData(report, baseQuery, query);
        break;
    }

    return report;
  }

  // Registration Analytics Methods
  private async addRegistrationData(
    report: DataManagementReportDto, 
    baseQuery: SelectQueryBuilder<UserAnalytics>, 
    query: DataManagementReportQueryDto
  ): Promise<void> {
    report.registration_stats = await this.calculateRegistrationStats(baseQuery);
    report.registration_time_series = await this.getRegistrationTimeSeries(baseQuery, query.time_period || 'daily');
    
    if (query.include_source_analysis) {
      report.source_analysis = await this.getRegistrationSourceAnalysis(baseQuery);
    }
    
    if (query.include_department_analysis) {
      report.department_analysis = await this.getDepartmentAnalysis(baseQuery);
    }
  }

  private async calculateRegistrationStats(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<RegistrationStatsDto> {
    const today = new Date();
    const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const totalRegistrations = await queryBuilder.getCount();
    
    const todayRegistrations = await queryBuilder
      .clone()
      .andWhere('DATE(analytics.registration_date) = DATE(:today)', { today })
      .getCount();
    
    const weekRegistrations = await queryBuilder
      .clone()
      .andWhere('analytics.registration_date >= :startOfWeek', { startOfWeek })
      .getCount();
    
    const monthRegistrations = await queryBuilder
      .clone()
      .andWhere('analytics.registration_date >= :startOfMonth', { startOfMonth })
      .getCount();

    // Calculate average acquisition cost
    const avgAcquisitionCostResult = await queryBuilder
      .clone()
      .select('AVG(analytics.acquisition_cost)', 'avg_cost')
      .getRawOne();

    return {
      total_registrations: totalRegistrations,
      today_registrations: todayRegistrations,
      week_registrations: weekRegistrations,
      month_registrations: monthRegistrations,
      growth_rate: 0, // Would need previous period data
      conversion_rate: 0, // Would need visit data
      avg_acquisition_cost: parseFloat(avgAcquisitionCostResult?.avg_cost) || 0,
      avg_registration_time: 0, // Would need timing data
      success_rate: 100, // Assuming all records are successful
    };
  }

  private async getRegistrationTimeSeries(
    queryBuilder: SelectQueryBuilder<UserAnalytics>,
    timePeriod: string
  ): Promise<RegistrationTimeSeriesDto[]> {
    const formatMap = {
      daily: 'YYYY-MM-DD',
      weekly: 'YYYY-WW',
      monthly: 'YYYY-MM',
      quarterly: 'YYYY-Q',
      yearly: 'YYYY'
    };

    const format = formatMap[timePeriod] || 'YYYY-MM-DD';

    const result = await queryBuilder
      .select([
        `TO_CHAR(analytics.registration_date, '${format}') as date`,
        'COUNT(analytics.id) as registrations',
        'COUNT(analytics.id) as successful_registrations',
        'AVG(analytics.acquisition_cost) as acquisition_cost'
      ])
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany();

    return result.map(item => ({
      date: item.date,
      registrations: parseInt(item.registrations) || 0,
      successful_registrations: parseInt(item.successful_registrations) || 0,
      success_rate: 100,
      acquisition_cost: parseFloat(item.acquisition_cost) || 0,
      conversion_rate: 0,
    }));
  }

  private async getRegistrationSourceAnalysis(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<RegistrationSourceAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.registration_source as source',
        'COUNT(analytics.id) as registrations_count',
        'AVG(analytics.acquisition_cost) as avg_acquisition_cost'
      ])
      .groupBy('analytics.registration_source')
      .orderBy('registrations_count', 'DESC')
      .getRawMany();

    const totalRegistrations = result.reduce((sum, item) => sum + parseInt(item.registrations_count || 0), 0);

    return result.map(item => ({
      source: item.source,
      source_name: this.getSourceName(item.source),
      registrations_count: parseInt(item.registrations_count) || 0,
      percentage: totalRegistrations > 0 ? (parseInt(item.registrations_count || 0) / totalRegistrations) * 100 : 0,
      avg_acquisition_cost: parseFloat(item.avg_acquisition_cost) || 0,
      conversion_rate: 0,
      avg_registration_time: 0,
      profile_completion_rate: 0,
      quality_score: 0,
    }));
  }

  // Member Analytics Methods
  private async addMemberAnalyticsData(
    report: DataManagementReportDto, 
    baseQuery: SelectQueryBuilder<UserAnalytics>, 
    query: DataManagementReportQueryDto
  ): Promise<void> {
    report.member_stats = await this.calculateMemberStats(baseQuery);
    report.member_status_distribution = await this.getMemberStatusDistribution(baseQuery);
    report.activity_level_distribution = await this.getActivityLevelDistribution(baseQuery);
  }

  private async calculateMemberStats(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<MemberAnalyticsStatsDto> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const stats = await queryBuilder
      .select([
        'COUNT(analytics.id) as total_members',
        'COUNT(CASE WHEN analytics.registration_date >= :thirtyDaysAgo THEN 1 END) as new_members',
        'COUNT(CASE WHEN analytics.days_since_last_login <= 7 THEN 1 END) as active_members',
        'COUNT(CASE WHEN analytics.churn_probability > 70 THEN 1 END) as at_risk_members',
        'COUNT(CASE WHEN analytics.lifetime_value > 10000000 THEN 1 END) as high_value_members',
        'AVG(analytics.lifetime_value) as avg_lifetime_value',
        'AVG(analytics.avg_session_duration) as avg_session_duration'
      ])
      .setParameter('thirtyDaysAgo', thirtyDaysAgo)
      .getRawOne();

    const totalMembers = parseInt(stats.total_members) || 0;
    const activeMembers = parseInt(stats.active_members) || 0;
    const atRiskMembers = parseInt(stats.at_risk_members) || 0;

    return {
      total_members: totalMembers,
      new_members: parseInt(stats.new_members) || 0,
      active_members: activeMembers,
      at_risk_members: atRiskMembers,
      high_value_members: parseInt(stats.high_value_members) || 0,
      activity_rate: totalMembers > 0 ? (activeMembers / totalMembers) * 100 : 0,
      churn_rate: totalMembers > 0 ? (atRiskMembers / totalMembers) * 100 : 0,
      avg_lifetime_value: parseFloat(stats.avg_lifetime_value) || 0,
      avg_engagement_score: 0, // Would need to calculate
      avg_daily_session_time: parseFloat(stats.avg_session_duration) || 0,
    };
  }

  private async getMemberStatusDistribution(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<MemberStatusDistributionDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.member_status as status',
        'COUNT(analytics.id) as count',
        'AVG(analytics.lifetime_value) as avg_lifetime_value'
      ])
      .groupBy('analytics.member_status')
      .orderBy('count', 'DESC')
      .getRawMany();

    const totalMembers = result.reduce((sum, item) => sum + parseInt(item.count || 0), 0);

    return result.map(item => ({
      status: item.status,
      status_name: this.getStatusName(item.status),
      count: parseInt(item.count) || 0,
      percentage: totalMembers > 0 ? (parseInt(item.count || 0) / totalMembers) * 100 : 0,
      avg_lifetime_value: parseFloat(item.avg_lifetime_value) || 0,
      avg_days_in_status: 0, // Would need historical data
    }));
  }

  private async getActivityLevelDistribution(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<MemberActivityLevelDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.activity_level as level',
        'COUNT(analytics.id) as count',
        'AVG(analytics.total_sessions) as avg_sessions',
        'AVG(analytics.avg_session_duration) as avg_duration'
      ])
      .groupBy('analytics.activity_level')
      .orderBy('count', 'DESC')
      .getRawMany();

    const totalMembers = result.reduce((sum, item) => sum + parseInt(item.count || 0), 0);

    return result.map(item => ({
      level: item.level,
      level_name: this.getActivityLevelName(item.level),
      count: parseInt(item.count) || 0,
      percentage: totalMembers > 0 ? (parseInt(item.count || 0) / totalMembers) * 100 : 0,
      avg_engagement_score: 0, // Would need to calculate
      avg_daily_sessions: parseFloat(item.avg_sessions) || 0,
      avg_session_duration: parseFloat(item.avg_duration) || 0,
    }));
  }

  // FTD Analysis Methods
  private async addFTDAnalysisData(
    report: DataManagementReportDto, 
    baseQuery: SelectQueryBuilder<UserAnalytics>, 
    query: DataManagementReportQueryDto
  ): Promise<void> {
    report.ftd_stats = await this.calculateFTDStats(baseQuery);
    report.ftd_time_series = await this.getFTDTimeSeries(baseQuery, query.time_period || 'daily');
    
    if (query.include_source_analysis) {
      report.ftd_source_analysis = await this.getFTDSourceAnalysis(baseQuery);
    }
  }

  private async calculateFTDStats(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<FTDAnalysisStatsDto> {
    const today = new Date();
    const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const stats = await queryBuilder
      .select([
        'COUNT(CASE WHEN analytics.first_deposit_amount > 0 THEN 1 END) as total_ftd',
        'SUM(analytics.first_deposit_amount) as total_ftd_amount',
        'AVG(analytics.first_deposit_amount) as avg_ftd_amount',
        'AVG(analytics.days_to_first_deposit) as avg_days_to_ftd'
      ])
      .getRawOne();

    const totalRegistrations = await queryBuilder.getCount();
    const totalFTD = parseInt(stats.total_ftd) || 0;

    const todayFTD = await queryBuilder
      .clone()
      .andWhere('DATE(analytics.first_deposit_date) = DATE(:today)', { today })
      .andWhere('analytics.first_deposit_amount > 0')
      .getCount();

    const weekFTD = await queryBuilder
      .clone()
      .andWhere('analytics.first_deposit_date >= :startOfWeek', { startOfWeek })
      .andWhere('analytics.first_deposit_amount > 0')
      .getCount();

    const monthFTD = await queryBuilder
      .clone()
      .andWhere('analytics.first_deposit_date >= :startOfMonth', { startOfMonth })
      .andWhere('analytics.first_deposit_amount > 0')
      .getCount();

    return {
      total_ftd: totalFTD,
      total_ftd_amount: parseFloat(stats.total_ftd_amount) || 0,
      avg_ftd_amount: parseFloat(stats.avg_ftd_amount) || 0,
      ftd_conversion_rate: totalRegistrations > 0 ? (totalFTD / totalRegistrations) * 100 : 0,
      avg_registration_to_ftd_days: parseFloat(stats.avg_days_to_ftd) || 0,
      today_ftd: todayFTD,
      week_ftd: weekFTD,
      month_ftd: monthFTD,
      ftd_growth_rate: 0, // Would need previous period data
    };
  }

  private async getFTDTimeSeries(
    queryBuilder: SelectQueryBuilder<UserAnalytics>,
    timePeriod: string
  ): Promise<FTDTimeSeriesDto[]> {
    const formatMap = {
      daily: 'YYYY-MM-DD',
      weekly: 'YYYY-WW',
      monthly: 'YYYY-MM',
      quarterly: 'YYYY-Q',
      yearly: 'YYYY'
    };

    const format = formatMap[timePeriod] || 'YYYY-MM-DD';

    const result = await queryBuilder
      .select([
        `TO_CHAR(analytics.first_deposit_date, '${format}') as date`,
        'COUNT(CASE WHEN analytics.first_deposit_amount > 0 THEN 1 END) as ftd_count',
        'SUM(analytics.first_deposit_amount) as ftd_amount',
        'AVG(analytics.first_deposit_amount) as avg_ftd_amount',
        'AVG(analytics.days_to_first_deposit) as avg_days_to_ftd'
      ])
      .where('analytics.first_deposit_date IS NOT NULL')
      .groupBy('date')
      .orderBy('date', 'ASC')
      .getRawMany();

    return result.map(item => ({
      date: item.date,
      ftd_count: parseInt(item.ftd_count) || 0,
      ftd_amount: parseFloat(item.ftd_amount) || 0,
      avg_ftd_amount: parseFloat(item.avg_ftd_amount) || 0,
      ftd_rate: 0, // Would need registration data for same period
      avg_days_to_ftd: parseFloat(item.avg_days_to_ftd) || 0,
    }));
  }

  private async getFTDSourceAnalysis(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<FTDSourceAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.registration_source as source',
        'COUNT(CASE WHEN analytics.first_deposit_amount > 0 THEN 1 END) as ftd_count',
        'SUM(analytics.first_deposit_amount) as total_ftd_amount',
        'AVG(analytics.first_deposit_amount) as avg_ftd_amount',
        'AVG(analytics.days_to_first_deposit) as avg_days_to_ftd',
        'COUNT(analytics.id) as total_registrations'
      ])
      .groupBy('analytics.registration_source')
      .orderBy('ftd_count', 'DESC')
      .getRawMany();

    return result.map(item => {
      const ftdCount = parseInt(item.ftd_count) || 0;
      const totalRegistrations = parseInt(item.total_registrations) || 0;
      
      return {
        source: item.source,
        source_name: this.getSourceName(item.source),
        ftd_count: ftdCount,
        total_ftd_amount: parseFloat(item.total_ftd_amount) || 0,
        avg_ftd_amount: parseFloat(item.avg_ftd_amount) || 0,
        ftd_conversion_rate: totalRegistrations > 0 ? (ftdCount / totalRegistrations) * 100 : 0,
        avg_days_to_ftd: parseFloat(item.avg_days_to_ftd) || 0,
        ftd_roi: 0, // Would need cost data
      };
    });
  }

  // Geographic Analysis
  private async getGeographicAnalysis(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<GeographicAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.region as region',
        'COUNT(analytics.id) as registrations',
        'COUNT(CASE WHEN analytics.first_deposit_amount > 0 THEN 1 END) as ftd_count',
        'SUM(analytics.first_deposit_amount) as ftd_amount',
        'AVG(analytics.lifetime_value) as avg_lifetime_value'
      ])
      .where('analytics.region IS NOT NULL')
      .groupBy('analytics.region')
      .orderBy('registrations', 'DESC')
      .getRawMany();

    const totalRegistrations = result.reduce((sum, item) => sum + parseInt(item.registrations || 0), 0);

    return result.map(item => {
      const registrations = parseInt(item.registrations) || 0;
      const ftdCount = parseInt(item.ftd_count) || 0;
      
      return {
        region: item.region,
        region_name: this.getRegionName(item.region),
        registrations,
        ftd_count: ftdCount,
        ftd_amount: parseFloat(item.ftd_amount) || 0,
        ftd_rate: registrations > 0 ? (ftdCount / registrations) * 100 : 0,
        avg_lifetime_value: parseFloat(item.avg_lifetime_value) || 0,
        avg_engagement_score: 0, // Would need to calculate
        percentage: totalRegistrations > 0 ? (registrations / totalRegistrations) * 100 : 0,
      };
    });
  }

  // Department Analysis
  private async getDepartmentAnalysis(queryBuilder: SelectQueryBuilder<UserAnalytics>): Promise<DepartmentAnalysisDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.department_id',
        'department.name as department_name',
        'COUNT(analytics.id) as registrations',
        'COUNT(CASE WHEN analytics.first_deposit_amount > 0 THEN 1 END) as ftd_count',
        'SUM(analytics.first_deposit_amount) as ftd_amount',
        'SUM(analytics.acquisition_cost) as total_acquisition_cost'
      ])
      .leftJoin('analytics.department', 'department')
      .where('analytics.department_id IS NOT NULL')
      .groupBy('analytics.department_id, department.name')
      .orderBy('registrations', 'DESC')
      .getRawMany();

    const totalRegistrations = result.reduce((sum, item) => sum + parseInt(item.registrations || 0), 0);

    return result.map(item => {
      const registrations = parseInt(item.registrations) || 0;
      const ftdCount = parseInt(item.ftd_count) || 0;
      const ftdAmount = parseFloat(item.ftd_amount) || 0;
      const acquisitionCost = parseFloat(item.total_acquisition_cost) || 0;
      
      return {
        department_id: item.department_id,
        department_name: item.department_name || 'Unknown',
        registrations,
        ftd_count: ftdCount,
        ftd_amount: ftdAmount,
        ftd_rate: registrations > 0 ? (ftdCount / registrations) * 100 : 0,
        total_acquisition_cost: acquisitionCost,
        roi: acquisitionCost > 0 ? ((ftdAmount - acquisitionCost) / acquisitionCost) * 100 : 0,
        percentage: totalRegistrations > 0 ? (registrations / totalRegistrations) * 100 : 0,
        efficiency_score: this.calculateEfficiencyScore(registrations, ftdCount, ftdAmount, acquisitionCost),
      };
    });
  }

  // Top Performers
  private async getTopPerformers(queryBuilder: SelectQueryBuilder<UserAnalytics>, limit: number): Promise<TopPerformerDto[]> {
    const result = await queryBuilder
      .select([
        'analytics.user_id',
        'user.username',
        'user.email',
        'analytics.registration_date',
        'analytics.lifetime_value',
        'analytics.total_deposits',
        'analytics.deposit_count',
        'analytics.registration_source',
        'department.name as department_name'
      ])
      .orderBy('analytics.lifetime_value', 'DESC')
      .limit(limit)
      .getRawMany();

    return result.map(item => ({
      user_id: item.user_id,
      username: item.username,
      email: item.email,
      registration_date: item.registration_date,
      lifetime_value: parseFloat(item.lifetime_value) || 0,
      total_deposits: parseFloat(item.total_deposits) || 0,
      deposit_count: parseInt(item.deposit_count) || 0,
      engagement_score: 0, // Would need to calculate
      department_name: item.department_name || 'Unknown',
      registration_source: item.registration_source,
    }));
  }

  // Lifecycle and Comprehensive Data
  private async addLifecycleData(
    report: DataManagementReportDto, 
    baseQuery: SelectQueryBuilder<UserAnalytics>, 
    query: DataManagementReportQueryDto
  ): Promise<void> {
    // Combine member analytics and FTD data for lifecycle view
    await this.addMemberAnalyticsData(report, baseQuery, query);
    await this.addFTDAnalysisData(report, baseQuery, query);
  }

  private async addAllReportData(
    report: DataManagementReportDto, 
    baseQuery: SelectQueryBuilder<UserAnalytics>, 
    query: DataManagementReportQueryDto
  ): Promise<void> {
    // Add all data sections
    await this.addRegistrationData(report, baseQuery, query);
    await this.addMemberAnalyticsData(report, baseQuery, query);
    await this.addFTDAnalysisData(report, baseQuery, query);

    if (query.include_geographic_analysis) {
      report.geographic_analysis = await this.getGeographicAnalysis(baseQuery);
    }

    if (query.include_department_analysis) {
      report.department_analysis = await this.getDepartmentAnalysis(baseQuery);
    }

    if (query.include_top_performers) {
      report.top_performers = await this.getTopPerformers(baseQuery, query.top_performers_limit || 10);
    }

    if (query.compare_previous) {
      report.comparison = await this.getComparison(query, report);
    }
  }

  // Helper Methods
  private createAnalyticsQueryBuilder(filters: Partial<UserAnalyticsQueryDto>): SelectQueryBuilder<UserAnalytics> {
    const queryBuilder = this.userAnalyticsRepository
      .createQueryBuilder('analytics')
      .leftJoinAndSelect('analytics.user', 'user')
      .leftJoinAndSelect('analytics.department', 'department')
      .leftJoinAndSelect('analytics.referred_by_user', 'referred_by_user');

    this.applyAnalyticsFilters(queryBuilder, filters);
    this.applyAnalyticsSorting(queryBuilder, filters);

    return queryBuilder;
  }

  private applyAnalyticsFilters(queryBuilder: SelectQueryBuilder<UserAnalytics>, filters: Partial<UserAnalyticsQueryDto>): void {
    const {
      search,
      user_id,
      department_id,
      registration_source,
      member_status,
      activity_level,
      region,
      registration_date_from,
      registration_date_to,
      last_login_date_from,
      last_login_date_to,
      first_deposit_date_from,
      first_deposit_date_to,
      min_member_tier,
      max_member_tier,
      min_lifetime_value,
      max_lifetime_value,
      min_churn_probability,
      max_churn_probability,
      min_total_logins,
      max_total_logins,
      min_days_since_last_login,
      max_days_since_last_login,
      min_total_deposits,
      max_total_deposits,
      min_deposit_count,
      max_deposit_count,
      min_total_bet_amount,
      max_total_bet_amount,
      min_win_rate,
      max_win_rate,
      new_users_only,
      active_users_only,
      high_value_only,
      at_risk_only,
      deposited_only,
      kyc_verified_only,
      suspicious_only,
      country,
      city,
      age_group,
      gender,
      registration_campaign,
      utm_source,
      utm_campaign,
      customer_segment,
    } = filters;

    // Search filter
    if (search) {
      queryBuilder.andWhere(
        '(user.username ILIKE :search OR user.email ILIKE :search OR analytics.referral_code ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // ID filters
    if (user_id) {
      queryBuilder.andWhere('analytics.user_id = :user_id', { user_id });
    }

    if (department_id) {
      queryBuilder.andWhere('analytics.department_id = :department_id', { department_id });
    }

    // Enum filters
    if (registration_source) {
      queryBuilder.andWhere('analytics.registration_source = :registration_source', { registration_source });
    }

    if (member_status) {
      queryBuilder.andWhere('analytics.member_status = :member_status', { member_status });
    }

    if (activity_level) {
      queryBuilder.andWhere('analytics.activity_level = :activity_level', { activity_level });
    }

    if (region) {
      queryBuilder.andWhere('analytics.region = :region', { region });
    }

    // Date filters
    if (registration_date_from) {
      queryBuilder.andWhere('analytics.registration_date >= :registration_date_from', { registration_date_from });
    }

    if (registration_date_to) {
      queryBuilder.andWhere('analytics.registration_date <= :registration_date_to', { registration_date_to });
    }

    if (last_login_date_from) {
      queryBuilder.andWhere('analytics.last_login_date >= :last_login_date_from', { last_login_date_from });
    }

    if (last_login_date_to) {
      queryBuilder.andWhere('analytics.last_login_date <= :last_login_date_to', { last_login_date_to });
    }

    if (first_deposit_date_from) {
      queryBuilder.andWhere('analytics.first_deposit_date >= :first_deposit_date_from', { first_deposit_date_from });
    }

    if (first_deposit_date_to) {
      queryBuilder.andWhere('analytics.first_deposit_date <= :first_deposit_date_to', { first_deposit_date_to });
    }

    // Numeric range filters
    if (min_member_tier !== undefined) {
      queryBuilder.andWhere('analytics.member_tier >= :min_member_tier', { min_member_tier });
    }

    if (max_member_tier !== undefined) {
      queryBuilder.andWhere('analytics.member_tier <= :max_member_tier', { max_member_tier });
    }

    if (min_lifetime_value !== undefined) {
      queryBuilder.andWhere('analytics.lifetime_value >= :min_lifetime_value', { min_lifetime_value });
    }

    if (max_lifetime_value !== undefined) {
      queryBuilder.andWhere('analytics.lifetime_value <= :max_lifetime_value', { max_lifetime_value });
    }

    if (min_churn_probability !== undefined) {
      queryBuilder.andWhere('analytics.churn_probability >= :min_churn_probability', { min_churn_probability });
    }

    if (max_churn_probability !== undefined) {
      queryBuilder.andWhere('analytics.churn_probability <= :max_churn_probability', { max_churn_probability });
    }

    if (min_total_logins !== undefined) {
      queryBuilder.andWhere('analytics.total_logins >= :min_total_logins', { min_total_logins });
    }

    if (max_total_logins !== undefined) {
      queryBuilder.andWhere('analytics.total_logins <= :max_total_logins', { max_total_logins });
    }

    if (min_days_since_last_login !== undefined) {
      queryBuilder.andWhere('analytics.days_since_last_login >= :min_days_since_last_login', { min_days_since_last_login });
    }

    if (max_days_since_last_login !== undefined) {
      queryBuilder.andWhere('analytics.days_since_last_login <= :max_days_since_last_login', { max_days_since_last_login });
    }

    if (min_total_deposits !== undefined) {
      queryBuilder.andWhere('analytics.total_deposits >= :min_total_deposits', { min_total_deposits });
    }

    if (max_total_deposits !== undefined) {
      queryBuilder.andWhere('analytics.total_deposits <= :max_total_deposits', { max_total_deposits });
    }

    if (min_deposit_count !== undefined) {
      queryBuilder.andWhere('analytics.deposit_count >= :min_deposit_count', { min_deposit_count });
    }

    if (max_deposit_count !== undefined) {
      queryBuilder.andWhere('analytics.deposit_count <= :max_deposit_count', { max_deposit_count });
    }

    if (min_total_bet_amount !== undefined) {
      queryBuilder.andWhere('analytics.total_bet_amount >= :min_total_bet_amount', { min_total_bet_amount });
    }

    if (max_total_bet_amount !== undefined) {
      queryBuilder.andWhere('analytics.total_bet_amount <= :max_total_bet_amount', { max_total_bet_amount });
    }

    if (min_win_rate !== undefined) {
      queryBuilder.andWhere('analytics.win_rate >= :min_win_rate', { min_win_rate });
    }

    if (max_win_rate !== undefined) {
      queryBuilder.andWhere('analytics.win_rate <= :max_win_rate', { max_win_rate });
    }

    // Boolean filters
    if (new_users_only) {
      queryBuilder.andWhere('analytics.registration_date >= :thirtyDaysAgo', { 
        thirtyDaysAgo: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
      });
    }

    if (active_users_only) {
      queryBuilder.andWhere('analytics.days_since_last_login <= 7');
    }

    if (high_value_only) {
      queryBuilder.andWhere('analytics.lifetime_value > 10000000');
    }

    if (at_risk_only) {
      queryBuilder.andWhere('analytics.churn_probability > 70');
    }

    if (deposited_only) {
      queryBuilder.andWhere('analytics.deposit_count > 0');
    }

    if (kyc_verified_only) {
      queryBuilder.andWhere('analytics.kyc_verified = true');
    }

    if (suspicious_only) {
      queryBuilder.andWhere('analytics.suspicious_activity = true');
    }

    // Text filters
    if (country) {
      queryBuilder.andWhere('analytics.country ILIKE :country', { country: `%${country}%` });
    }

    if (city) {
      queryBuilder.andWhere('analytics.city ILIKE :city', { city: `%${city}%` });
    }

    if (age_group) {
      queryBuilder.andWhere('analytics.age_group = :age_group', { age_group });
    }

    if (gender) {
      queryBuilder.andWhere('analytics.gender = :gender', { gender });
    }

    if (registration_campaign) {
      queryBuilder.andWhere('analytics.registration_campaign ILIKE :registration_campaign', { 
        registration_campaign: `%${registration_campaign}%` 
      });
    }

    if (utm_source) {
      queryBuilder.andWhere('analytics.utm_source ILIKE :utm_source', { utm_source: `%${utm_source}%` });
    }

    if (utm_campaign) {
      queryBuilder.andWhere('analytics.utm_campaign ILIKE :utm_campaign', { utm_campaign: `%${utm_campaign}%` });
    }

    if (customer_segment) {
      queryBuilder.andWhere('analytics.customer_segment = :customer_segment', { customer_segment });
    }
  }

  private applyAnalyticsSorting(queryBuilder: SelectQueryBuilder<UserAnalytics>, filters: Partial<UserAnalyticsQueryDto>): void {
    const { sort_by = 'created_at', sort_order = 'DESC' } = filters;

    const validSortFields = [
      'registration_date',
      'last_login_date',
      'member_status',
      'activity_level',
      'lifetime_value',
      'churn_probability',
      'total_logins',
      'days_since_last_login',
      'total_deposits',
      'deposit_count',
      'first_deposit_amount',
      'total_bet_amount',
      'win_rate',
      'created_at',
      'updated_at'
    ];

    if (validSortFields.includes(sort_by)) {
      queryBuilder.orderBy(`analytics.${sort_by}`, sort_order);
    } else {
      queryBuilder.orderBy('analytics.created_at', 'DESC');
    }
  }

  private applyReportFilters(queryBuilder: SelectQueryBuilder<UserAnalytics>, filters: DataManagementReportQueryDto): void {
    const { start_date, end_date, department_id, registration_source, member_status, region } = filters;

    if (start_date) {
      queryBuilder.andWhere('analytics.registration_date >= :start_date', { start_date });
    }

    if (end_date) {
      queryBuilder.andWhere('analytics.registration_date <= :end_date', { end_date });
    }

    if (department_id) {
      queryBuilder.andWhere('analytics.department_id = :department_id', { department_id });
    }

    if (registration_source) {
      queryBuilder.andWhere('analytics.registration_source = :registration_source', { registration_source });
    }

    if (member_status) {
      queryBuilder.andWhere('analytics.member_status = :member_status', { member_status });
    }

    if (region) {
      queryBuilder.andWhere('analytics.region = :region', { region });
    }
  }

  private async getComparison(query: DataManagementReportQueryDto, currentReport: DataManagementReportDto): Promise<DataManagementComparisonDto> {
    // This would need implementation for previous period data
    return {
      current_period: {
        registrations: currentReport.registration_stats || {} as RegistrationStatsDto,
        members: currentReport.member_stats || {} as MemberAnalyticsStatsDto,
        ftd: currentReport.ftd_stats || {} as FTDAnalysisStatsDto,
      },
      previous_period: {
        registrations: currentReport.registration_stats || {} as RegistrationStatsDto, // Placeholder
        members: currentReport.member_stats || {} as MemberAnalyticsStatsDto, // Placeholder
        ftd: currentReport.ftd_stats || {} as FTDAnalysisStatsDto, // Placeholder
      },
      registrations_change_percentage: 0,
      active_members_change_percentage: 0,
      ftd_change_percentage: 0,
      ftd_revenue_change_percentage: 0,
      acquisition_cost_change_percentage: 0,
      roi_change_percentage: 0,
    };
  }

  private transformToResponseDto(analytics: UserAnalytics): UserAnalyticsResponseDto {
    return plainToClass(UserAnalyticsResponseDto, {
      ...analytics,
      isNewUser: analytics.isNewUser,
      isActiveUser: analytics.isActiveUser,
      isHighValue: analytics.isHighValue,
      isAtRisk: analytics.isAtRisk,
      hasDeposited: analytics.hasDeposited,
      daysSinceRegistration: analytics.daysSinceRegistration,
      registrationToFirstDepositDays: analytics.registrationToFirstDepositDays,
      avgSessionDurationMinutes: analytics.avgSessionDurationMinutes,
      formattedLifetimeValue: analytics.formattedLifetimeValue,
      formattedTotalDeposits: analytics.formattedTotalDeposits,
      profitLoss: analytics.profitLoss,
      formattedProfitLoss: analytics.formattedProfitLoss,
      engagementScore: analytics.engagementScore,
      customerLifetimeStage: analytics.customerLifetimeStage,
    });
  }

  // Utility methods for getting display names
  private getSourceName(source: RegistrationSource): string {
    const sourceNames = {
      [RegistrationSource.WEBSITE]: 'Website',
      [RegistrationSource.MOBILE_APP]: 'Mobile App',
      [RegistrationSource.SOCIAL_MEDIA]: 'Social Media',
      [RegistrationSource.REFERRAL]: 'Referral',
      [RegistrationSource.ADVERTISEMENT]: 'Advertisement',
      [RegistrationSource.EMAIL_CAMPAIGN]: 'Email Campaign',
      [RegistrationSource.SMS_CAMPAIGN]: 'SMS Campaign',
      [RegistrationSource.AFFILIATE]: 'Affiliate',
      [RegistrationSource.ORGANIC_SEARCH]: 'Organic Search',
      [RegistrationSource.PAID_SEARCH]: 'Paid Search',
      [RegistrationSource.DIRECT]: 'Direct',
      [RegistrationSource.OTHER]: 'Other',
    };
    return sourceNames[source] || 'Unknown';
  }

  private getStatusName(status: MemberStatus): string {
    const statusNames = {
      [MemberStatus.NEW]: 'New Member',
      [MemberStatus.ACTIVE]: 'Active Member',
      [MemberStatus.INACTIVE]: 'Inactive Member',
      [MemberStatus.VIP]: 'VIP Member',
      [MemberStatus.SUSPENDED]: 'Suspended',
      [MemberStatus.BANNED]: 'Banned',
      [MemberStatus.CHURNED]: 'Churned',
      [MemberStatus.HIGH_VALUE]: 'High Value',
      [MemberStatus.AT_RISK]: 'At Risk',
    };
    return statusNames[status] || 'Unknown';
  }

  private getActivityLevelName(level: ActivityLevel): string {
    const levelNames = {
      [ActivityLevel.VERY_LOW]: 'Very Low',
      [ActivityLevel.LOW]: 'Low',
      [ActivityLevel.MEDIUM]: 'Medium',
      [ActivityLevel.HIGH]: 'High',
      [ActivityLevel.VERY_HIGH]: 'Very High',
    };
    return levelNames[level] || 'Unknown';
  }

  private getRegionName(region: GeographicRegion): string {
    const regionNames = {
      [GeographicRegion.NORTH]: 'North',
      [GeographicRegion.SOUTH]: 'South',
      [GeographicRegion.CENTRAL]: 'Central',
      [GeographicRegion.NORTHEAST]: 'Northeast',
      [GeographicRegion.NORTHWEST]: 'Northwest',
      [GeographicRegion.SOUTHEAST]: 'Southeast',
      [GeographicRegion.SOUTHWEST]: 'Southwest',
      [GeographicRegion.MEKONG_DELTA]: 'Mekong Delta',
      [GeographicRegion.INTERNATIONAL]: 'International',
    };
    return regionNames[region] || 'Unknown';
  }

  private calculateEfficiencyScore(registrations: number, ftdCount: number, ftdAmount: number, acquisitionCost: number): number {
    if (registrations === 0) return 0;
    
    const conversionRate = (ftdCount / registrations) * 100;
    const roi = acquisitionCost > 0 ? ((ftdAmount - acquisitionCost) / acquisitionCost) * 100 : 0;
    const avgFtdAmount = ftdCount > 0 ? ftdAmount / ftdCount : 0;
    
    // Composite score based on conversion rate, ROI, and average FTD amount
    const conversionScore = Math.min(conversionRate * 2, 100);
    const roiScore = Math.min(Math.max(roi, 0), 100);
    const amountScore = Math.min(avgFtdAmount / 100000, 100); // Assuming 100k VND as baseline
    
    return (conversionScore + roiScore + amountScore) / 3;
  }

  private formatReportPeriod(startDate: string, endDate: string): string {
    if (startDate && endDate) {
      return `${startDate} to ${endDate}`;
    }
    return 'All time';
  }
} 