#!/bin/bash

# Fixed curl request with proper JSON syntax
curl -X 'POST' \
  'http://localhost:3000/api/v1/user-menu-assignments/bulk-assign' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao' \
  -H 'Content-Type: application/json' \
  -d '{
  "userId": "3a17c8d4-1ca1-4b67-8e7e-01f6b9e2f367",
  "departmentId": "994e8535-c11b-4060-a4ec-7060e2991c3e",
  "roleId": "97227721-8068-4f16-9dc7-c29502997ff0",
  "menu": [
    {
      "id": "089b77e7-9b5a-42d0-bc17-96e698ea4c1c",
      "permissionIds": [
        "16aa76c6-e8ce-440b-ad56-1f8b8df8198c",
        "a371d638-9ded-4259-b457-5f849d72f082"
      ]
    }
  ],
  "subMenu": [
    {
      "id": "5e8817d8-9175-4fb9-b647-7686f00e0356",
      "permissionIds": [
        "d48e77d4-9b01-497a-b3fe-38378808f922",
        "d030bfdd-1a54-4269-8d20-590d96ccce12"
      ]
    }
  ]
}'
