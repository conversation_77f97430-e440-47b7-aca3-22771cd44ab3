### Debug User Menu Assignments - Step by step testing
### Use this file to debug issues step by step

@baseUrl = http://localhost:3000/api/v1
@token = your-jwt-token-here

### Step 1: Login and get token
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

###

### Step 2: Check if endpoints are available
GET {{baseUrl}}/user-menu-assignments
Authorization: Bearer {{token}}

###

### Step 3: Get sample data for testing
GET {{baseUrl}}/users?limit=1
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/menus?limit=1
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/permissions?limit=2
Authorization: Bearer {{token}}

###

GET {{baseUrl}}/roles?limit=1
Authorization: Bearer {{token}}

###

### Step 4: Test bulkAssign with minimal data
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bear<PERSON> {{token}}
Content-Type: application/json

{
  "userId": "replace-with-actual-user-id",
  "roleId": "replace-with-actual-role-id",
  "menu": [
    {
      "id": "replace-with-actual-menu-id",
      "permissionIds": [
        "replace-with-actual-permission-id"
      ]
    }
  ]
}

###

### Step 5: Check if assignment was created
GET {{baseUrl}}/user-menu-assignments?limit=5
Authorization: Bearer {{token}}

###

### Step 6: Test updateBulkAssign
PATCH {{baseUrl}}/user-menu-assignments/replace-with-assignment-id/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "replace-with-actual-user-id",
  "roleId": "replace-with-actual-role-id",
  "menu": [
    {
      "id": "replace-with-actual-menu-id",
      "permissionIds": [
        "replace-with-different-permission-id"
      ]
    }
  ]
}

###

### Step 7: Test deleteBulk
DELETE {{baseUrl}}/user-menu-assignments/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [
    "replace-with-assignment-id-1",
    "replace-with-assignment-id-2"
  ]
}

###

### Debug: Check server logs for errors
### If you get 500 errors, check the terminal where you ran yarn start:dev

### Debug: Test with curl (alternative to HTTP client)
### curl -X POST "http://localhost:3000/api/v1/user-menu-assignments/bulk-assign" \
###   -H "Authorization: Bearer YOUR_TOKEN" \
###   -H "Content-Type: application/json" \
###   -d '{
###     "userId": "your-user-id",
###     "roleId": "your-role-id",
###     "menu": [{
###       "id": "your-menu-id",
###       "permissionIds": ["your-permission-id"]
###     }]
###   }'

### Debug: Check database directly (if needed)
### SELECT * FROM user_menu_assignments ORDER BY created_at DESC LIMIT 5;
### SELECT * FROM user_role_assignments ORDER BY created_at DESC LIMIT 5;

### Common Issues and Solutions:
### 1. 401 Unauthorized - Check if token is valid and not expired
### 2. 403 Forbidden - Check if user has SUPER_ADMIN or MANAGER role
### 3. 400 Bad Request - Check if all required fields are provided and UUIDs are valid
### 4. 404 Not Found - Check if the assignment ID exists for updateBulkAssign
### 5. 500 Internal Server Error - Check server logs for detailed error message

### Test Authentication
GET {{baseUrl}}/auth/profile
Authorization: Bearer {{token}}

###

### Test Authorization - This should work for SUPER_ADMIN/MANAGER
GET {{baseUrl}}/user-menu-assignments
Authorization: Bearer {{token}}

###

### Validate UUIDs format (should be like: 123e4567-e89b-12d3-a456-************)
### Invalid UUID example (should return 400):
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "invalid-uuid-format",
  "roleId": "also-invalid",
  "menu": [
    {
      "id": "bad-menu-id",
      "permissionIds": ["bad-permission-id"]
    }
  ]
}

###

### Test with missing required fields (should return 400):
POST {{baseUrl}}/user-menu-assignments/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "123e4567-e89b-12d3-a456-************"
}

###

### Test updateBulkAssign with invalid assignment ID (should return 404):
PATCH {{baseUrl}}/user-menu-assignments/00000000-0000-0000-0000-000000000000/bulk-assign
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "userId": "123e4567-e89b-12d3-a456-************",
  "roleId": "123e4567-e89b-12d3-a456-************",
  "menu": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "permissionIds": ["123e4567-e89b-12d3-a456-************"]
    }
  ]
}
