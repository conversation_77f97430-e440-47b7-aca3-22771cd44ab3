import { IsOptional, IsString, IsEnum, IsNumber, IsDateString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AdPlatform, AdType, AdStatus, AdObjective } from '../../../entities/ad-performance.entity';

export class AdPerformanceQueryDto {
  @ApiPropertyOptional({ description: 'Số trang', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Số lượng mỗi trang', minimum: 1, maximum: 100, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Tì<PERSON> kiếm theo tên chiến dịch, ad set, hoặc ad name' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ enum: AdPlatform, description: 'Lọc theo nền tảng' })
  @IsOptional()
  @IsEnum(AdPlatform)
  platform?: AdPlatform;

  @ApiPropertyOptional({ enum: AdType, description: 'Lọc theo loại quảng cáo' })
  @IsOptional()
  @IsEnum(AdType)
  ad_type?: AdType;

  @ApiPropertyOptional({ enum: AdStatus, description: 'Lọc theo trạng thái' })
  @IsOptional()
  @IsEnum(AdStatus)
  status?: AdStatus;

  @ApiPropertyOptional({ enum: AdObjective, description: 'Lọc theo mục tiêu' })
  @IsOptional()
  @IsEnum(AdObjective)
  objective?: AdObjective;

  @ApiPropertyOptional({ description: 'Lọc theo ID phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;

  @ApiPropertyOptional({ description: 'Lọc theo người tạo' })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  // Date filters
  @ApiPropertyOptional({ description: 'Ngày bắt đầu (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  start_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày bắt đầu (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  start_date_to?: string;

  @ApiPropertyOptional({ description: 'Ngày kết thúc (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  end_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày kết thúc (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  end_date_to?: string;

  @ApiPropertyOptional({ description: 'Ngày báo cáo (từ)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  report_date_from?: string;

  @ApiPropertyOptional({ description: 'Ngày báo cáo (đến)', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  report_date_to?: string;

  // Performance filters
  @ApiPropertyOptional({ description: 'Số lần hiển thị tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_impressions?: number;

  @ApiPropertyOptional({ description: 'Số lần hiển thị tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_impressions?: number;

  @ApiPropertyOptional({ description: 'Số lần click tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_clicks?: number;

  @ApiPropertyOptional({ description: 'Số lần click tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_clicks?: number;

  @ApiPropertyOptional({ description: 'Số lần chuyển đổi tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_conversions?: number;

  @ApiPropertyOptional({ description: 'Số lần chuyển đổi tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_conversions?: number;

  // Cost filters
  @ApiPropertyOptional({ description: 'Tổng chi phí tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_cost?: number;

  @ApiPropertyOptional({ description: 'Tổng chi phí tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_cost?: number;

  @ApiPropertyOptional({ description: 'CPA tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_cpa?: number;

  @ApiPropertyOptional({ description: 'CPA tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_cpa?: number;

  // Performance ratio filters
  @ApiPropertyOptional({ description: 'CTR tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_ctr?: number;

  @ApiPropertyOptional({ description: 'CTR tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_ctr?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ chuyển đổi tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_conversion_rate?: number;

  @ApiPropertyOptional({ description: 'Tỷ lệ chuyển đổi tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_conversion_rate?: number;

  @ApiPropertyOptional({ description: 'ROAS tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_roas?: number;

  @ApiPropertyOptional({ description: 'ROAS tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_roas?: number;

  @ApiPropertyOptional({ description: 'ROI tối thiểu (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  min_roi?: number;

  @ApiPropertyOptional({ description: 'ROI tối đa (%)' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  max_roi?: number;

  // Revenue filters
  @ApiPropertyOptional({ description: 'Doanh thu tối thiểu' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  min_revenue?: number;

  @ApiPropertyOptional({ description: 'Doanh thu tối đa' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  max_revenue?: number;

  // Additional filters
  @ApiPropertyOptional({ description: 'Lọc theo vị trí mục tiêu' })
  @IsOptional()
  @IsString()
  target_location?: string;

  @ApiPropertyOptional({ description: 'Lọc theo độ tuổi' })
  @IsOptional()
  @IsString()
  age_range?: string;

  @ApiPropertyOptional({ description: 'Lọc theo giới tính' })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị các chiến dịch có lợi nhuận' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  profitable_only?: boolean;

  @ApiPropertyOptional({ description: 'Chỉ hiển thị các chiến dịch đang hoạt động' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  active_only?: boolean;

  // Sorting
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: [
      'campaign_name',
      'platform',
      'status',
      'start_date',
      'end_date',
      'impressions',
      'clicks',
      'conversions',
      'total_cost',
      'total_revenue',
      'click_through_rate',
      'conversion_rate',
      'cost_per_acquisition',
      'return_on_ad_spend',
      'return_on_investment',
      'created_at',
      'updated_at'
    ]
  })
  @IsOptional()
  @IsString()
  sort_by?: string;

  @ApiPropertyOptional({
    description: 'Thứ tự sắp xếp',
    enum: ['ASC', 'DESC'],
    default: 'DESC'
  })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sort_order?: 'ASC' | 'DESC' = 'DESC';
} 