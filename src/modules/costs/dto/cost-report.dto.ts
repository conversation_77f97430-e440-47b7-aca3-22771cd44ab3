import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CostType, CostStatus } from '../../../entities/cost.entity';

export enum CostReportPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export class CostReportQueryDto {
  @ApiProperty({ description: 'Từ ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: '<PERSON>ến ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: 'ID bộ phận', required: false })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiProperty({ description: 'Tên bộ phận', required: false })
  @IsOptional()
  @IsString()
  departmentName?: string;

  @ApiProperty({ description: 'Loại chi phí', enum: CostType, required: false })
  @IsOptional()
  @IsEnum(CostType)
  type?: CostType;

  @ApiProperty({ description: 'Trạng thái', enum: CostStatus, required: false })
  @IsOptional()
  @IsEnum(CostStatus)
  status?: CostStatus;

  @ApiProperty({ 
    description: 'Khoảng thời gian báo cáo', 
    enum: CostReportPeriod, 
    default: CostReportPeriod.MONTHLY 
  })
  @IsOptional()
  @IsEnum(CostReportPeriod)
  period?: CostReportPeriod;

  @ApiProperty({ description: 'Trang hiện tại', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiProperty({ description: 'Số lượng mỗi trang', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number;
}

export class CostDepartmentStatsDto {
  @ApiProperty({ description: 'ID bộ phận' })
  departmentId: string;

  @ApiProperty({ description: 'Tên bộ phận' })
  departmentName: string;

  @ApiProperty({ description: 'Tổng số chi phí' })
  totalCostCount: number;

  @ApiProperty({ description: 'Tổng số tiền' })
  totalAmount: number;

  @ApiProperty({ description: 'Số tiền trung bình' })
  averageAmount: number;

  @ApiProperty({ description: 'Số tiền cao nhất' })
  maxAmount: number;

  @ApiProperty({ description: 'Số tiền thấp nhất' })
  minAmount: number;

  @ApiProperty({ description: 'Số chi phí đã duyệt' })
  approvedCount: number;

  @ApiProperty({ description: 'Số chi phí chờ duyệt' })
  pendingCount: number;

  @ApiProperty({ description: 'Số chi phí đã thanh toán' })
  paidCount: number;

  @ApiProperty({ description: 'Số chi phí quá hạn' })
  overdueCount: number;

  @ApiProperty({ description: 'Tỷ lệ duyệt (%)' })
  approvalRate: number;

  @ApiProperty({ description: 'Tỷ lệ thanh toán (%)' })
  paymentRate: number;

  @ApiProperty({ description: 'Thống kê theo loại chi phí' })
  costTypeBreakdown: {
    [key in CostType]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt: Date;

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt: Date;
}

export class CostTimeSeriesDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số lượng chi phí' })
  costCount: number;

  @ApiProperty({ description: 'Tổng số tiền' })
  totalAmount: number;

  @ApiProperty({ description: 'Số tiền trung bình' })
  averageAmount: number;

  @ApiProperty({ description: 'Số chi phí đã duyệt' })
  approvedCount: number;

  @ApiProperty({ description: 'Số chi phí đã thanh toán' })
  paidCount: number;
}

export class CostTopSpendersDto {
  @ApiProperty({ description: 'ID bộ phận' })
  departmentId: string;

  @ApiProperty({ description: 'Tên bộ phận' })
  departmentName: string;

  @ApiProperty({ description: 'Tổng số tiền' })
  totalAmount: number;

  @ApiProperty({ description: 'Số lượng chi phí' })
  costCount: number;

  @ApiProperty({ description: 'Số tiền trung bình' })
  averageAmount: number;

  @ApiProperty({ description: 'Thứ hạng' })
  rank: number;

  @ApiProperty({ description: '% so với tổng' })
  percentage: number;
}

export class CostReportSummaryDto {
  @ApiProperty({ description: 'Tổng số chi phí' })
  totalCostCount: number;

  @ApiProperty({ description: 'Tổng số tiền' })
  totalAmount: number;

  @ApiProperty({ description: 'Số tiền trung bình' })
  averageAmount: number;

  @ApiProperty({ description: 'Số chi phí đã duyệt' })
  approvedCount: number;

  @ApiProperty({ description: 'Số chi phí chờ duyệt' })
  pendingCount: number;

  @ApiProperty({ description: 'Số chi phí đã thanh toán' })
  paidCount: number;

  @ApiProperty({ description: 'Số chi phí quá hạn' })
  overdueCount: number;

  @ApiProperty({ description: 'Tỷ lệ duyệt (%)' })
  approvalRate: number;

  @ApiProperty({ description: 'Tỷ lệ thanh toán (%)' })
  paymentRate: number;

  @ApiProperty({ description: 'Tăng trưởng so với kỳ trước (%)' })
  growthRate: number;

  @ApiProperty({ description: 'Số bộ phận' })
  totalDepartments: number;

  @ApiProperty({ description: 'Thống kê theo loại chi phí' })
  typeBreakdown: {
    [key in CostType]: {
      count: number;
      amount: number;
      percentage: number;
    };
  };
}

export class CostReportResponseDto {
  @ApiProperty({ description: 'Tóm tắt báo cáo' })
  summary: CostReportSummaryDto;

  @ApiProperty({ description: 'Thống kê theo bộ phận', type: [CostDepartmentStatsDto] })
  departmentStats: CostDepartmentStatsDto[];

  @ApiProperty({ description: 'Dữ liệu theo thời gian', type: [CostTimeSeriesDto] })
  timeSeries: CostTimeSeriesDto[];

  @ApiProperty({ description: 'Top bộ phận chi tiêu nhiều nhất', type: [CostTopSpendersDto] })
  topSpenders: CostTopSpendersDto[];

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  dateRange: {
    startDate: string;
    endDate: string;
  };

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  period: CostReportPeriod;

  @ApiProperty({ description: 'Thông tin phân trang' })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({ description: 'Thời gian tạo báo cáo' })
  generatedAt: Date;
}

export class ApproveCostDto {
  @ApiProperty({ description: 'Có duyệt hay không' })
  isApproved: boolean;

  @ApiProperty({ description: 'Lý do từ chối (nếu từ chối)', required: false })
  @IsOptional()
  @IsString()
  rejectionReason?: string;

  @ApiProperty({ description: 'Ghi chú duyệt', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
} 