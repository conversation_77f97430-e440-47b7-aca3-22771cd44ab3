import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseInterceptors,
  UploadedFile,
  Res,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBearerAuth } from '@nestjs/swagger';
import { Response } from 'express';
import { DepositsService } from './deposits.service';
import { CreateDepositDto } from './dto/create-deposit.dto';
import { UpdateDepositDto } from './dto/update-deposit.dto';
import { DepositChartDto } from './dto/deposit-chart.dto';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { DepositResponseDto } from './dto/deposit-response.dto';
import { FTDReportQueryDto, FTDReportResponseDto } from './dto/ftd-report.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { RolesGuard } from '@/common/guards/roles.guard';
import { PermissionsGuard } from '@/common/guards/permissions.guard';
import { Roles } from '@/common/decorators/roles.decorator';
import { Permissions } from '@/common/decorators/permissions.decorator';
import { PermissionsType, RoleType } from '@/common/constants';
import { CurrentUser } from '@/common/decorators';
import { User } from '@/entities';

@ApiTags('deposits')
@Controller('deposits')
@UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class DepositsController {
  constructor(private readonly depositsService: DepositsService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo giao dịch nạp tiền mới' })
  @ApiResponse({ status: 201, description: 'Tạo thành công', type: DepositResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.CREATE)
  create(@Body() createDepositDto: CreateDepositDto, @CurrentUser() user: User) {
    return this.depositsService.create(createDepositDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách giao dịch nạp tiền' })
  @ApiResponse({ status: 200, description: 'Lấy thành công' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findAll(@Query() paginationDto: PaginationDto) {
    return this.depositsService.findAll(paginationDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết giao dịch nạp tiền' })
  @ApiResponse({ status: 200, description: 'Lấy thành công', type: DepositResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.READ)
  findOne(@Param('id') id: string) {
    return this.depositsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật giao dịch nạp tiền' })
  @ApiResponse({ status: 200, description: 'Cập nhật thành công', type: DepositResponseDto })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.UPDATE)
  update(@Param('id') id: string, @Body() updateDepositDto: UpdateDepositDto) {
    return this.depositsService.update(id, updateDepositDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa giao dịch nạp tiền' })
  @ApiResponse({ status: 200, description: 'Xóa thành công' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.DELETE)
  remove(@Param('id') id: string) {
    return this.depositsService.remove(id);
  }

  @Post('import')
  @ApiOperation({ summary: 'Import dữ liệu từ file Excel' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.IMPORT)
  async importFromExcel(@UploadedFile() file: any) {
    return this.depositsService.importFromExcel(file);
  }

  @Get('export/excel')
  @ApiOperation({ summary: 'Export dữ liệu ra file Excel' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.EXPORT)
  async exportToExcel(@Res() res: Response) {
    const fileName = await this.depositsService.exportToExcel();
    const filePath = `uploads/temp/${fileName}`;
    
    res.download(filePath, fileName, (err) => {
      if (err) {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Error downloading file',
        });
      }
    });
  }

  @Get('chart/data')
  @ApiOperation({ summary: 'Lấy dữ liệu biểu đồ theo thời gian' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getChartData(@Query() chartDto: DepositChartDto) {
    return this.depositsService.getChartData(chartDto);
  }

  @Get('stats/department')
  @ApiOperation({ summary: 'Lấy thống kê theo bộ phận' })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER, RoleType.TEAM_MEMBER)
  @Permissions(PermissionsType.ANALYTICS)
  async getDepartmentStats() {
    return this.depositsService.getDepartmentStats();
  }

  @Get('ftd-report')
  @ApiOperation({ summary: 'Lấy báo cáo FTD (First Time Deposit) theo bộ phận' })
  @ApiResponse({ 
    status: 200, 
    description: 'Báo cáo FTD thành công',
    type: FTDReportResponseDto 
  })
  @Roles(RoleType.SUPER_ADMIN, RoleType.MANAGER, RoleType.TEAM_LEADER)
  @Permissions(PermissionsType.READ)
  async getFTDReport(@Query() queryDto: FTDReportQueryDto) {
    return await this.depositsService.getFTDReport(queryDto);
  }
} 