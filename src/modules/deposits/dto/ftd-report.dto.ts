import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString, IsEnum, IsNumber, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export enum FTDReportPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export class FTDReportQueryDto {
  @ApiProperty({ description: 'Từ ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: 'Đến ngày (YYYY-MM-DD)', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: 'ID bộ phận', required: false })
  @IsOptional()
  @IsString()
  departmentId?: string;

  @ApiProperty({ description: 'Tên bộ phận', required: false })
  @IsOptional()
  @IsString()
  departmentName?: string;

  @ApiProperty({ 
    description: 'Khoảng thời gian báo cáo', 
    enum: FTDReportPeriod, 
    default: FTDReportPeriod.DAILY 
  })
  @IsOptional()
  @IsEnum(FTDReportPeriod)
  period?: FTDReportPeriod;

  @ApiProperty({ description: 'Trang hiện tại', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiProperty({ description: 'Số lượng mỗi trang', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number;
}

export class FTDDepartmentStatsDto {
  @ApiProperty({ description: 'ID bộ phận' })
  departmentId: number;

  @ApiProperty({ description: 'Tên bộ phận' })
  departmentName: string;

  @ApiProperty({ description: 'Tổng số lượng FTD' })
  totalFTDCount: number;

  @ApiProperty({ description: 'Tổng số tiền FTD' })
  totalFTDAmount: number;

  @ApiProperty({ description: 'Số tiền FTD trung bình' })
  averageFTDAmount: number;

  @ApiProperty({ description: 'Tổng số người dùng duy nhất' })
  totalUniqueUsers: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftdRate: number;

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt: Date;

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt: Date;
}

export class FTDTimeSeriesDto {
  @ApiProperty({ description: 'Ngày' })
  date: string;

  @ApiProperty({ description: 'Số lượng FTD' })
  ftdCount: number;

  @ApiProperty({ description: 'Số tiền FTD' })
  ftdAmount: number;

  @ApiProperty({ description: 'Số người dùng mới' })
  newUsersCount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftdRate: number;
}

export class FTDTopPerformersDto {
  @ApiProperty({ description: 'ID bộ phận' })
  departmentId: number;

  @ApiProperty({ description: 'Tên bộ phận' })
  departmentName: string;

  @ApiProperty({ description: 'Tổng số tiền FTD' })
  totalFTDAmount: number;

  @ApiProperty({ description: 'Số lượng FTD' })
  ftdCount: number;

  @ApiProperty({ description: 'Tỷ lệ FTD (%)' })
  ftdRate: number;

  @ApiProperty({ description: 'Thứ hạng' })
  rank: number;
}

export class FTDReportSummaryDto {
  @ApiProperty({ description: 'Tổng số lượng FTD' })
  totalFTDCount: number;

  @ApiProperty({ description: 'Tổng số tiền FTD' })
  totalFTDAmount: number;

  @ApiProperty({ description: 'Số tiền FTD trung bình' })
  averageFTDAmount: number;

  @ApiProperty({ description: 'Tổng số người dùng mới' })
  totalNewUsers: number;

  @ApiProperty({ description: 'Tỷ lệ FTD tổng thể (%)' })
  overallFTDRate: number;

  @ApiProperty({ description: 'Tăng trưởng so với kỳ trước (%)' })
  growthRate: number;

  @ApiProperty({ description: 'Số bộ phận' })
  totalDepartments: number;
}

export class FTDReportResponseDto {
  @ApiProperty({ description: 'Tóm tắt báo cáo' })
  summary: FTDReportSummaryDto;

  @ApiProperty({ description: 'Thống kê theo bộ phận', type: [FTDDepartmentStatsDto] })
  departmentStats: FTDDepartmentStatsDto[];

  @ApiProperty({ description: 'Dữ liệu theo thời gian', type: [FTDTimeSeriesDto] })
  timeSeries: FTDTimeSeriesDto[];

  @ApiProperty({ description: 'Top bộ phận hiệu quả nhất', type: [FTDTopPerformersDto] })
  topPerformers: FTDTopPerformersDto[];

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  dateRange: {
    startDate: string;
    endDate: string;
  };

  @ApiProperty({ description: 'Khoảng thời gian báo cáo' })
  period: FTDReportPeriod;

  @ApiProperty({ description: 'Thông tin phân trang' })
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  @ApiProperty({ description: 'Thời gian tạo báo cáo' })
  generatedAt: Date;
} 