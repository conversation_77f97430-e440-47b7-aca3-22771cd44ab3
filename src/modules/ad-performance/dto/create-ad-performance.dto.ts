import { IsString, IsEnum, <PERSON>Number, IsOptional, IsDateString, IsUUID, IsNotEmpty, Min, Max, IsUrl, Length } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AdPlatform, AdType, AdStatus, AdObjective } from '../../../entities/ad-performance.entity';

export class CreateAdPerformanceDto {
  @ApiProperty({ description: 'Tên chiến dịch quảng cáo' })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  campaign_name: string;

  @ApiPropertyOptional({ description: 'Tên ad set' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  ad_set_name?: string;

  @ApiPropertyOptional({ description: 'Tên quảng cáo' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  ad_name?: string;

  @ApiProperty({ enum: AdPlatform, description: 'Nền tảng quảng cáo' })
  @IsEnum(AdPlatform)
  platform: AdPlatform;

  @ApiProperty({ enum: AdType, description: 'Loại quảng cáo' })
  @IsEnum(AdType)
  ad_type: AdType;

  @ApiProperty({ enum: AdStatus, description: 'Trạng thái quảng cáo' })
  @IsEnum(AdStatus)
  status: AdStatus;

  @ApiProperty({ enum: AdObjective, description: 'Mục tiêu quảng cáo' })
  @IsEnum(AdObjective)
  objective: AdObjective;

  // Performance Metrics
  @ApiProperty({ description: 'Số lần hiển thị', minimum: 0 })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  impressions: number;

  @ApiProperty({ description: 'Số lần click', minimum: 0 })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  clicks: number;

  @ApiProperty({ description: 'Số lần chuyển đổi', minimum: 0 })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  conversions: number;

  @ApiPropertyOptional({ description: 'Số leads', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  leads?: number;

  @ApiPropertyOptional({ description: 'Số đăng ký', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  registrations?: number;

  @ApiPropertyOptional({ description: 'Số deposits', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  deposits?: number;

  @ApiPropertyOptional({ description: 'Số first time deposits', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  first_time_deposits?: number;

  // Cost Metrics
  @ApiProperty({ description: 'Tổng chi phí', minimum: 0 })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total_cost: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi click', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_click?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi nghìn lần hiển thị (CPM)', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_mille?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi chuyển đổi (CPA)', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_acquisition?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi lead', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_lead?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi đăng ký', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_registration?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi deposit', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_deposit?: number;

  @ApiPropertyOptional({ description: 'Chi phí mỗi FTD', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  cost_per_ftd?: number;

  // Revenue Metrics
  @ApiPropertyOptional({ description: 'Tổng doanh thu', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total_revenue?: number;

  @ApiPropertyOptional({ description: 'Tổng tiền deposit', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  deposit_amount?: number;

  @ApiPropertyOptional({ description: 'Tổng tiền FTD', minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  ftd_amount?: number;

  // Time Period
  @ApiProperty({ description: 'Ngày bắt đầu chiến dịch', type: 'string', format: 'date' })
  @IsDateString()
  start_date: string;

  @ApiProperty({ description: 'Ngày kết thúc chiến dịch', type: 'string', format: 'date' })
  @IsDateString()
  end_date: string;

  @ApiProperty({ description: 'Ngày báo cáo', type: 'string', format: 'date' })
  @IsDateString()
  report_date: string;

  // Targeting Information
  @ApiPropertyOptional({ description: 'Đối tượng mục tiêu' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  target_audience?: string;

  @ApiPropertyOptional({ description: 'Vị trí mục tiêu' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  target_location?: string;

  @ApiPropertyOptional({ description: 'Độ tuổi mục tiêu' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  age_range?: string;

  @ApiPropertyOptional({ description: 'Giới tính mục tiêu' })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  gender?: string;

  @ApiPropertyOptional({ description: 'Sở thích mục tiêu' })
  @IsOptional()
  @IsString()
  interests?: string;

  @ApiPropertyOptional({ description: 'Từ khóa mục tiêu' })
  @IsOptional()
  @IsString()
  keywords?: string;

  @ApiPropertyOptional({ description: 'Đơn vị tiền tệ', default: 'VND' })
  @IsOptional()
  @IsString()
  @Length(1, 10)
  currency?: string;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({ description: 'URL chiến dịch' })
  @IsOptional()
  @IsUrl()
  campaign_url?: string;

  @ApiPropertyOptional({ description: 'URL landing page' })
  @IsOptional()
  @IsUrl()
  landing_page_url?: string;

  @ApiPropertyOptional({ description: 'ID phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;
} 