import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdPerformanceController } from './ad-performance.controller';
import { AdPerformanceService } from './ad-performance.service';
import { AdPerformance } from '../../entities/ad-performance.entity';
import { Department } from '../../entities/department.entity';
import { User } from '../../entities/user.entity';
import { CommonServicesModule } from '../../common/services/common-services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AdPerformance, Department, User]),
    CommonServicesModule,
  ],
  controllers: [AdPerformanceController],
  providers: [AdPerformanceService],
  exports: [AdPerformanceService],
})
export class AdPerformanceModule {} 