import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostsService } from './costs.service';
import { CostsController } from './costs.controller';
import { Cost } from '../../entities/cost.entity';
import { Department } from '../../entities/department.entity';
import { User } from '../../entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Cost, Department, User])
  ],
  controllers: [CostsController],
  providers: [CostsService],
  exports: [CostsService]
})
export class CostsModule {} 