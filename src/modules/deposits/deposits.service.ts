import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Deposit } from '@/entities/deposit.entity';
import { Department } from '@/entities/department.entity';
import { CreateDepositDto } from './dto/create-deposit.dto';
import { UpdateDepositDto } from './dto/update-deposit.dto';
import { DepositChartDto } from './dto/deposit-chart.dto';
import { createPaginatedResult, PaginationDto } from '@/common/dto/pagination.dto';
import { User } from '@/entities';
import { 
  FTDReportQueryDto, 
  FTDReportResponseDto, 
  FTDReportSummaryDto, 
  FTDDepartmentStatsDto, 
  FTDTimeSeriesDto, 
  FTDTopPerformersDto, 
  FTDReportPeriod 
} from './dto/ftd-report.dto';

@Injectable()
export class DepositsService {
  constructor(
    @InjectRepository(Deposit)
    private depositRepository: Repository<Deposit>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async create(createDepositDto: CreateDepositDto, user: User): Promise<any> {
    const departmentId = user.departmentId || null;

    const department = await this.departmentRepository.findOne({
      where: { id: departmentId ?? createDepositDto.department_id }
    });
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const depositData: any = { ...createDepositDto };

    depositData.department = department;

    const deposit = this.depositRepository.create(depositData);

    return await this.depositRepository.save(deposit);

  }

  async findAll(paginationDto: PaginationDto): Promise<{ data: Deposit[]; total: number }> {
    const { page = 1, limit = 10, search } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.depositRepository
      .createQueryBuilder('deposit')
      .leftJoinAndSelect('deposit.department', 'department');

    if (search) {
      queryBuilder.where(
        'deposit.transaction_id LIKE :search OR deposit.payment_method LIKE :search OR deposit.status LIKE :search',
        { search: `%${search}%` }
      );
    }

    const [data, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('deposit.created_at', 'DESC')
      .getManyAndCount();
    
    return createPaginatedResult(data, total, page || 1, limit || 10);
  }

  async findOne(id: string): Promise<Deposit> {
    const deposit = await this.depositRepository.findOne({
      where: { id },
      relations: ['department']
    });

    if (!deposit) {
      throw new NotFoundException('Deposit not found');
    }

    return deposit;
  }

  async update(id: string, updateDepositDto: UpdateDepositDto): Promise<Deposit> {
    const deposit = await this.findOne(id);

    if (updateDepositDto.department_id) {
      const department = await this.departmentRepository.findOne({
        where: { id: updateDepositDto.department_id }
      });
      if (!department) {
        throw new NotFoundException('Department not found');
      }
    }

    Object.assign(deposit, updateDepositDto);
    return await this.depositRepository.save(deposit);
  }

  async remove(id: string): Promise<void> {
    const deposit = await this.findOne(id);
    await this.depositRepository.remove(deposit);
  }

  async importFromExcel(file: any): Promise<{ success: number; errors: string[] }> {
    // Placeholder for import functionality
    // In a real implementation, you would use a library like xlsx
    const errors: string[] = [];
    let success = 0;

    // Mock implementation
    return { success, errors };
  }

  async exportToExcel(): Promise<string> {
    const deposits = await this.depositRepository.find({
      relations: ['department'],
      order: { createdAt: 'DESC' }
    });

    const data = deposits.map(deposit => ({
      id: deposit.id,
      amount: deposit.amount,
      transaction_id: deposit.transaction_id,
      payment_method: deposit.payment_method,
      status: deposit.status,
      processed_at: deposit.processed_at,
      notes: deposit.notes,
      external_reference: deposit.external_reference,
      department_id: deposit.department_id,
      department_name: deposit.department?.name,
      created_at: deposit.createdAt,
      updated_at: deposit.updatedAt,
    }));

    // Placeholder for export functionality
    // In a real implementation, you would use a library like xlsx
    const fileName = `deposits_${new Date().toISOString().split('T')[0]}.xlsx`;
    return fileName;
  }

  async getChartData(chartDto: DepositChartDto) {
    const { from_date, to_date, department_id, chart_type = 'daily' } = chartDto;
    
    let whereCondition: any = {};
    let dateFormat: string;

    if (from_date && to_date) {
      whereCondition.created_at = Between(new Date(from_date), new Date(to_date));
    }

    if (department_id) {
      whereCondition.department_id = parseInt(department_id);
    }

    switch (chart_type) {
      case 'daily':
        dateFormat = 'DATE(created_at)';
        break;
      case 'weekly':
        dateFormat = 'YEARWEEK(created_at)';
        break;
      case 'monthly':
        dateFormat = 'DATE_FORMAT(created_at, "%Y-%m")';
        break;
      default:
        dateFormat = 'DATE(created_at)';
    }

    const query = `
      SELECT 
        ${dateFormat} as date,
        SUM(amount) as total_amount,
        COUNT(*) as total_transactions,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'failed' THEN amount ELSE 0 END) as failed_amount
      FROM deposits
      WHERE 1=1
        ${from_date ? `AND created_at >= '${from_date}'` : ''}
        ${to_date ? `AND created_at <= '${to_date}'` : ''}
        ${department_id ? `AND department_id = ${department_id}` : ''}
      GROUP BY ${dateFormat}
      ORDER BY date DESC
    `;

    const result = await this.depositRepository.query(query);
    return result;
  }

  async getDepartmentStats() {
    const query = `
      SELECT 
        d.id,
        d.name as department_name,
        COUNT(dep.id) as total_deposits,
        SUM(dep.amount) as total_amount,
        AVG(dep.amount) as avg_amount,
        SUM(CASE WHEN dep.status = 'completed' THEN dep.amount ELSE 0 END) as completed_amount,
        SUM(CASE WHEN dep.status = 'pending' THEN dep.amount ELSE 0 END) as pending_amount
      FROM departments d
      LEFT JOIN deposits dep ON d.id = dep.department_id
      GROUP BY d.id, d.name
      ORDER BY total_amount DESC
    `;

    return await this.depositRepository.query(query);
  }

  async getFTDReport(queryDto: FTDReportQueryDto): Promise<FTDReportResponseDto> {
    const { 
      startDate, 
      endDate, 
      departmentId, 
      departmentName, 
      period = FTDReportPeriod.DAILY,
      page = 1,
      limit = 10
    } = queryDto;

    // Calculate date range
    const dateRange = this.calculateDateRange(startDate, endDate);

    // Build base query for FTD data
    const baseQuery = this.depositRepository
      .createQueryBuilder('deposit')
      .leftJoinAndSelect('deposit.department', 'department')
      .leftJoinAndSelect('deposit.user', 'user')
      .where('deposit.created_at BETWEEN :startDate AND :endDate', {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });

    // Apply department filter if specified
    if (departmentId) {
      baseQuery.andWhere('deposit.department_id = :departmentId', { departmentId: parseInt(departmentId) });
    }
    if (departmentName) {
      baseQuery.andWhere('department.name ILIKE :departmentName', { 
        departmentName: `%${departmentName}%` 
      });
    }

    // Get all deposits in the date range
    const deposits = await baseQuery.getMany();

    // Generate report data
    const [summary, departmentStats, timeSeries, topPerformers] = await Promise.all([
      this.generateFTDSummary(deposits, dateRange),
      this.generateFTDDepartmentStats(deposits, page, limit),
      this.generateFTDTimeSeries(deposits, period, dateRange),
      this.generateFTDTopPerformers(deposits)
    ]);

    // Calculate total pages for pagination
    const totalDepartments = await this.departmentRepository.count();
    const totalPages = Math.ceil(totalDepartments / limit);

    return {
      summary,
      departmentStats,
      timeSeries,
      topPerformers,
      dateRange: {
        startDate: dateRange.startDate.toISOString().split('T')[0],
        endDate: dateRange.endDate.toISOString().split('T')[0]
      },
      period,
      pagination: {
        page,
        limit,
        total: totalDepartments,
        totalPages
      },
      generatedAt: new Date()
    };
  }

  private calculateDateRange(startDate?: string, endDate?: string): { startDate: Date; endDate: Date } {
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    
    // Set time to start and end of day
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);
    
    return { startDate: start, endDate: end };
  }

  private async generateFTDSummary(deposits: Deposit[], dateRange: { startDate: Date; endDate: Date }): Promise<FTDReportSummaryDto> {
    // Get FTD deposits (first deposit for each user)
    const userFirstDeposits = new Map<string, Deposit>();
    
    for (const deposit of deposits) {
      if (deposit.user_id) {
        const existingDeposit = userFirstDeposits.get(deposit.user_id);
        if (!existingDeposit || deposit.createdAt < existingDeposit.createdAt) {
          userFirstDeposits.set(deposit.user_id, deposit);
        }
      }
    }

    const ftdDeposits = Array.from(userFirstDeposits.values());
    
    // Calculate summary statistics
    const totalFTDCount = ftdDeposits.length;
    const totalFTDAmount = ftdDeposits.reduce((sum, deposit) => sum + Number(deposit.amount), 0);
    const averageFTDAmount = totalFTDCount > 0 ? totalFTDAmount / totalFTDCount : 0;
    
    // Count unique users and total new users
    const uniqueUserIds = new Set(deposits.filter(d => d.user_id).map(d => d.user_id));
    const totalNewUsers = uniqueUserIds.size;
    
    const overallFTDRate = totalNewUsers > 0 ? (totalFTDCount / totalNewUsers) * 100 : 0;

    // Calculate growth rate (compare with previous period)
    const periodDuration = dateRange.endDate.getTime() - dateRange.startDate.getTime();
    const previousPeriodStart = new Date(dateRange.startDate.getTime() - periodDuration);
    const previousPeriodEnd = new Date(dateRange.endDate.getTime() - periodDuration);
    
    const previousPeriodDeposits = await this.depositRepository
      .createQueryBuilder('deposit')
      .where('deposit.created_at BETWEEN :startDate AND :endDate', {
        startDate: previousPeriodStart,
        endDate: previousPeriodEnd
      })
      .getMany();

    const previousFTDCount = this.calculateFTDCount(previousPeriodDeposits);
    const growthRate = previousFTDCount > 0 ? ((totalFTDCount - previousFTDCount) / previousFTDCount) * 100 : 0;

    const totalDepartments = await this.departmentRepository.count();

    return {
      totalFTDCount,
      totalFTDAmount,
      averageFTDAmount,
      totalNewUsers,
      overallFTDRate,
      growthRate,
      totalDepartments
    };
  }

  private async generateFTDDepartmentStats(deposits: Deposit[], page: number, limit: number): Promise<FTDDepartmentStatsDto[]> {
    const skip = (page - 1) * limit;
    
    // Get departments with pagination
    const departments = await this.departmentRepository.find({
      skip,
      take: limit,
      order: { name: 'ASC' }
    });

    const departmentStats: FTDDepartmentStatsDto[] = [];

    for (const department of departments) {
      const departmentDeposits = deposits.filter(d => d.department_id === Number(department.id));
      
      // Calculate FTD for this department
      const userFirstDeposits = new Map<string, Deposit>();
      
      for (const deposit of departmentDeposits) {
        if (deposit.user_id) {
          const existingDeposit = userFirstDeposits.get(deposit.user_id);
          if (!existingDeposit || deposit.createdAt < existingDeposit.createdAt) {
            userFirstDeposits.set(deposit.user_id, deposit);
          }
        }
      }

      const ftdDeposits = Array.from(userFirstDeposits.values());
      const totalFTDCount = ftdDeposits.length;
      const totalFTDAmount = ftdDeposits.reduce((sum, deposit) => sum + Number(deposit.amount), 0);
      const averageFTDAmount = totalFTDCount > 0 ? totalFTDAmount / totalFTDCount : 0;
      
      const uniqueUserIds = new Set(departmentDeposits.filter(d => d.user_id).map(d => d.user_id));
      const totalUniqueUsers = uniqueUserIds.size;
      const ftdRate = totalUniqueUsers > 0 ? (totalFTDCount / totalUniqueUsers) * 100 : 0;

      departmentStats.push({
        departmentId: Number(department.id),
        departmentName: department.name,
        totalFTDCount,
        totalFTDAmount,
        averageFTDAmount,
        totalUniqueUsers,
        ftdRate,
        createdAt: department.createdAt,
        updatedAt: department.updatedAt
      });
    }

    return departmentStats.sort((a, b) => b.totalFTDAmount - a.totalFTDAmount);
  }

  private async generateFTDTimeSeries(deposits: Deposit[], period: FTDReportPeriod, dateRange: { startDate: Date; endDate: Date }): Promise<FTDTimeSeriesDto[]> {
    const timeSeries: FTDTimeSeriesDto[] = [];
    
    // Generate date intervals based on period
    const intervals = this.generateDateIntervals(dateRange.startDate, dateRange.endDate, period);
    
    for (const interval of intervals) {
      const intervalDeposits = deposits.filter(d => 
        d.createdAt >= interval.start && d.createdAt <= interval.end
      );
      
      // Calculate FTD for this interval
      const userFirstDeposits = new Map<string, Deposit>();
      
      for (const deposit of intervalDeposits) {
        if (deposit.user_id) {
          const existingDeposit = userFirstDeposits.get(deposit.user_id);
          if (!existingDeposit || deposit.createdAt < existingDeposit.createdAt) {
            userFirstDeposits.set(deposit.user_id, deposit);
          }
        }
      }

      const ftdDeposits = Array.from(userFirstDeposits.values());
      const ftdCount = ftdDeposits.length;
      const ftdAmount = ftdDeposits.reduce((sum, deposit) => sum + Number(deposit.amount), 0);
      
      const uniqueUserIds = new Set(intervalDeposits.filter(d => d.user_id).map(d => d.user_id));
      const newUsersCount = uniqueUserIds.size;
      const ftdRate = newUsersCount > 0 ? (ftdCount / newUsersCount) * 100 : 0;

      timeSeries.push({
        date: interval.start.toISOString().split('T')[0],
        ftdCount,
        ftdAmount,
        newUsersCount,
        ftdRate
      });
    }
    
    return timeSeries;
  }

  private async generateFTDTopPerformers(deposits: Deposit[]): Promise<FTDTopPerformersDto[]> {
    // Group deposits by department
    const departmentMap = new Map<number, { name: string; deposits: Deposit[] }>();
    
    for (const deposit of deposits) {
      if (!departmentMap.has(deposit.department_id)) {
        departmentMap.set(deposit.department_id, {
          name: deposit.department.name,
          deposits: []
        });
      }
      departmentMap.get(deposit.department_id)!.deposits.push(deposit);
    }

    const topPerformers: FTDTopPerformersDto[] = [];

    for (const [departmentId, departmentData] of departmentMap.entries()) {
      // Calculate FTD for this department
      const userFirstDeposits = new Map<string, Deposit>();
      
      for (const deposit of departmentData.deposits) {
        if (deposit.user_id) {
          const existingDeposit = userFirstDeposits.get(deposit.user_id);
          if (!existingDeposit || deposit.createdAt < existingDeposit.createdAt) {
            userFirstDeposits.set(deposit.user_id, deposit);
          }
        }
      }

      const ftdDeposits = Array.from(userFirstDeposits.values());
      const ftdCount = ftdDeposits.length;
      const totalFTDAmount = ftdDeposits.reduce((sum, deposit) => sum + Number(deposit.amount), 0);
      
      const uniqueUserIds = new Set(departmentData.deposits.filter(d => d.user_id).map(d => d.user_id));
      const totalUniqueUsers = uniqueUserIds.size;
      const ftdRate = totalUniqueUsers > 0 ? (ftdCount / totalUniqueUsers) * 100 : 0;

      topPerformers.push({
        departmentId,
        departmentName: departmentData.name,
        totalFTDAmount,
        ftdCount,
        ftdRate,
        rank: 0 // Will be set after sorting
      });
    }

    // Sort by total FTD amount and assign ranks
    topPerformers.sort((a, b) => b.totalFTDAmount - a.totalFTDAmount);
    topPerformers.forEach((performer, index) => {
      performer.rank = index + 1;
    });

    return topPerformers.slice(0, 10); // Return top 10
  }

  private generateDateIntervals(startDate: Date, endDate: Date, period: FTDReportPeriod): { start: Date; end: Date }[] {
    const intervals: { start: Date; end: Date }[] = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const intervalStart = new Date(currentDate);
      let intervalEnd: Date;

      switch (period) {
        case FTDReportPeriod.DAILY:
          intervalEnd = new Date(currentDate);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setDate(currentDate.getDate() + 1);
          break;
        case FTDReportPeriod.WEEKLY:
          intervalEnd = new Date(currentDate);
          intervalEnd.setDate(intervalEnd.getDate() + 6);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setDate(currentDate.getDate() + 7);
          break;
        case FTDReportPeriod.MONTHLY:
          intervalEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setMonth(currentDate.getMonth() + 1);
          break;
        case FTDReportPeriod.YEARLY:
          intervalEnd = new Date(currentDate.getFullYear(), 11, 31);
          intervalEnd.setHours(23, 59, 59, 999);
          currentDate.setFullYear(currentDate.getFullYear() + 1);
          break;
      }

      intervals.push({ start: intervalStart, end: intervalEnd });
    }

    return intervals;
  }

  private calculateFTDCount(deposits: Deposit[]): number {
    const userFirstDeposits = new Map<string, Deposit>();
    
    for (const deposit of deposits) {
      if (deposit.user_id) {
        const existingDeposit = userFirstDeposits.get(deposit.user_id);
        if (!existingDeposit || deposit.createdAt < existingDeposit.createdAt) {
          userFirstDeposits.set(deposit.user_id, deposit);
        }
      }
    }

    return userFirstDeposits.size;
  }
} 