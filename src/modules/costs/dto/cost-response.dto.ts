import { ApiProperty } from '@nestjs/swagger';
import { CostType, CostStatus } from '../../../entities/cost.entity';

export class CostResponseDto {
  @ApiProperty({ description: 'ID chi phí' })
  id: string;

  @ApiProperty({ description: 'Tên chi phí' })
  name: string;

  @ApiProperty({ description: 'Mô tả chi phí' })
  description?: string;

  @ApiProperty({ description: 'Số tiền chi phí' })
  amount: number;

  @ApiProperty({ description: 'Số tiền chi phí đã format' })
  formattedAmount: string;

  @ApiProperty({ description: 'Đơn vị tiền tệ' })
  currency: string;

  @ApiProperty({ description: 'Loại chi phí', enum: CostType })
  type: CostType;

  @ApiProperty({ description: 'Trạng thái chi phí', enum: CostStatus })
  status: CostStatus;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> phát sinh chi phí' })
  costDate: Date;

  @ApiProperty({ description: 'Ngày thanh toán' })
  paymentDate?: Date;

  @ApiProperty({ description: 'Nhà cung cấp/Vendor' })
  vendor?: string;

  @ApiProperty({ description: 'Mã hóa đơn' })
  invoiceNumber?: string;

  @ApiProperty({ description: 'Link Google Drive chứa chứng từ' })
  googleDriveLink?: string;

  @ApiProperty({ description: 'Danh sách links Google Drive bổ sung' })
  additionalLinks?: string[];

  @ApiProperty({ description: 'Tên file trong Google Drive' })
  fileName?: string;

  @ApiProperty({ description: 'Loại file' })
  fileType?: string;

  @ApiProperty({ description: 'Ghi chú thêm' })
  notes?: string;

  @ApiProperty({ description: 'Đã được duyệt' })
  isApproved: boolean;

  @ApiProperty({ description: 'Ngày duyệt' })
  approvedAt?: Date;

  @ApiProperty({ description: 'Người duyệt' })
  approvedBy?: string;

  @ApiProperty({ description: 'Lý do từ chối' })
  rejectionReason?: string;

  @ApiProperty({ description: 'Có quá hạn không' })
  isOverdue: boolean;

  @ApiProperty({ description: 'Có tài liệu không' })
  hasDocuments: boolean;

  @ApiProperty({ description: 'Thông tin bộ phận' })
  department: {
    id: string;
    name: string;
  };

  @ApiProperty({ description: 'Người tạo' })
  createdByUser: {
    id: string;
    username: string;
    fullName: string;
  };

  @ApiProperty({ description: 'Người duyệt' })
  approvedByUser?: {
    id: string;
    username: string;
    fullName: string;
  };

  @ApiProperty({ description: 'Ngày tạo' })
  createdAt: Date;

  @ApiProperty({ description: 'Ngày cập nhật' })
  updatedAt: Date;
} 