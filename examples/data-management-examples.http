### Data Management Import/Export Examples
### Updated to use XLSX library for all file operations

### Variables
@baseUrl = http://localhost:3000
@authToken = your-jwt-token-here

### Import Users from Excel file
POST {{baseUrl}}/data-management/import
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="entityType"

users
--boundary123
Content-Disposition: form-data; name="skipFirstRow"

true
--boundary123
Content-Disposition: form-data; name="sheetName"

Users Data
--boundary123
Content-Disposition: form-data; name="file"; filename="users.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./sample-files/users.xlsx
--boundary123--

### Import Users from CSV file
POST {{baseUrl}}/data-management/import
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="entityType"

users
--boundary123
Content-Disposition: form-data; name="skipFirstRow"

true
--boundary123
Content-Disposition: form-data; name="delimiter"

,
--boundary123
Content-Disposition: form-data; name="encoding"

utf8
--boundary123
Content-Disposition: form-data; name="file"; filename="users.csv"
Content-Type: text/csv

< ./sample-files/users.csv
--boundary123--

### Import Affiliates from Excel file
POST {{baseUrl}}/data-management/import
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="entityType"

affiliates
--boundary123
Content-Disposition: form-data; name="skipFirstRow"

true
--boundary123
Content-Disposition: form-data; name="sheetName"

Affiliate Data
--boundary123
Content-Disposition: form-data; name="file"; filename="affiliates.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./sample-files/affiliates.xlsx
--boundary123--

### Import Bets from Excel file
POST {{baseUrl}}/data-management/import
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="entityType"

bets
--boundary123
Content-Disposition: form-data; name="skipFirstRow"

true
--boundary123
Content-Disposition: form-data; name="sheetName"

Betting Data
--boundary123
Content-Disposition: form-data; name="file"; filename="bets.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./sample-files/bets.xlsx
--boundary123--

### Import Deposits from Excel file
POST {{baseUrl}}/data-management/import
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="entityType"

deposits
--boundary123
Content-Disposition: form-data; name="skipFirstRow"

true
--boundary123
Content-Disposition: form-data; name="sheetName"

Deposit Records
--boundary123
Content-Disposition: form-data; name="file"; filename="deposits.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./sample-files/deposits.xlsx
--boundary123--

###############################################
# EXPORT EXAMPLES
###############################################

### Export Users to Excel
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "users",
  "format": "xlsx",
  "includeHeaders": true,
  "sheetName": "Users Export",
  "isActive": true,
  "createdFrom": "2024-01-01",
  "createdTo": "2024-12-31"
}

### Export Users to CSV
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "users",
  "format": "csv",
  "includeHeaders": true,
  "delimiter": ",",
  "isActive": true
}

### Export Affiliates to Excel with filters
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "affiliates",
  "format": "xlsx",
  "includeHeaders": true,
  "sheetName": "Active Affiliates",
  "isActive": true,
  "createdFrom": "2024-01-01"
}

### Export Bets to Excel
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "bets",
  "format": "xlsx",
  "includeHeaders": true,
  "sheetName": "Betting History",
  "createdFrom": "2024-01-01",
  "createdTo": "2024-12-31"
}

### Export Deposits to Excel
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "deposits",
  "format": "xlsx",
  "includeHeaders": true,
  "sheetName": "Deposit Records",
  "createdFrom": "2024-01-01",
  "createdTo": "2024-12-31"
}

### Export to JSON format
POST {{baseUrl}}/data-management/export
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "entityType": "users",
  "format": "json",
  "isActive": true
}

###############################################
# JOB MONITORING
###############################################

### Get Job Status
GET {{baseUrl}}/data-management/job/{job-id}/status
Authorization: Bearer {{authToken}}

### Get Job Progress
GET {{baseUrl}}/data-management/job/{job-id}/progress
Authorization: Bearer {{authToken}}

### Get Queue Statistics
GET {{baseUrl}}/data-management/queue/stats
Authorization: Bearer {{authToken}}

### Cancel Job
DELETE {{baseUrl}}/data-management/job/{job-id}
Authorization: Bearer {{authToken}}

### Retry Failed Job
POST {{baseUrl}}/data-management/job/{job-id}/retry
Authorization: Bearer {{authToken}}

###############################################
# SAMPLE DATA STRUCTURES
###############################################

# Excel Import Structure for Users:
# Column A: username
# Column B: email
# Column C: fullName
# Column D: phone
# Column E: departmentId
# Column F: teamId
# Column G: isActive

# Excel Import Structure for Affiliates:
# Column A: code
# Column B: name
# Column C: description
# Column D: commissionRate
# Column E: isActive

# Excel Import Structure for Bets:
# Column A: departmentId
# Column B: bet_amount
# Column C: win_amount
# Column D: loss_amount
# Column E: bet_id
# Column F: game_type
# Column G: game_name
# Column H: status
# Column I: notes
# Column J: external_reference

# Excel Import Structure for Deposits:
# Column A: user_id
# Column B: department_id
# Column C: amount
# Column D: transaction_id
# Column E: payment_method
# Column F: status
# Column G: notes
# Column H: external_reference 