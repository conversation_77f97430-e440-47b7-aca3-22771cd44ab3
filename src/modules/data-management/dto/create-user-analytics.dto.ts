import { IsString, IsEnum, IsNumber, IsOptional, IsDateString, IsUUID, IsNotEmpty, Min, Max, IsBoolean, IsArray, Length, IsIP } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RegistrationSource, MemberStatus, ActivityLevel, GeographicRegion } from '../../../entities/user-analytics.entity';

export class CreateUserAnalyticsDto {
  @ApiProperty({ description: 'ID người dùng' })
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({ description: 'Ngày đăng ký', type: 'string', format: 'date' })
  @IsDateString()
  registration_date: string;

  @ApiProperty({ enum: RegistrationSource, description: 'Nguồn đăng ký' })
  @IsEnum(RegistrationSource)
  registration_source: RegistrationSource;

  @ApiPropertyOptional({ description: '<PERSON>ên chiến dịch đăng ký' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  registration_campaign?: string;

  @ApiPropertyOptional({ description: 'Mã giới thiệu' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  referral_code?: string;

  @ApiPropertyOptional({ description: 'ID người giới thiệu' })
  @IsOptional()
  @IsUUID()
  referred_by_user_id?: string;

  @ApiPropertyOptional({ description: 'IP đăng ký' })
  @IsOptional()
  @IsIP()
  registration_ip?: string;

  @ApiPropertyOptional({ description: 'Thiết bị đăng ký' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  registration_device?: string;

  @ApiPropertyOptional({ description: 'Trình duyệt đăng ký' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  registration_browser?: string;

  @ApiPropertyOptional({ enum: MemberStatus, description: 'Trạng thái thành viên', default: MemberStatus.NEW })
  @IsOptional()
  @IsEnum(MemberStatus)
  member_status?: MemberStatus;

  @ApiPropertyOptional({ enum: ActivityLevel, description: 'Mức độ hoạt động', default: ActivityLevel.LOW })
  @IsOptional()
  @IsEnum(ActivityLevel)
  activity_level?: ActivityLevel;

  @ApiPropertyOptional({ description: 'Hạng thành viên', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  member_tier?: number;

  @ApiPropertyOptional({ description: 'Giá trị lifetime', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  lifetime_value?: number;

  @ApiPropertyOptional({ description: 'Giá trị dự đoán', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  predicted_value?: number;

  @ApiPropertyOptional({ description: 'Xác suất churn (%)', minimum: 0, maximum: 100, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Type(() => Number)
  churn_probability?: number;

  // Activity Metrics
  @ApiPropertyOptional({ description: 'Tổng số lần đăng nhập', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total_logins?: number;

  @ApiPropertyOptional({ description: 'Ngày đăng nhập cuối', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  last_login_date?: string;

  @ApiPropertyOptional({ description: 'Số ngày kể từ lần đăng nhập cuối', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  days_since_last_login?: number;

  @ApiPropertyOptional({ description: 'Tổng số phiên làm việc', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total_sessions?: number;

  @ApiPropertyOptional({ description: 'Thời gian phiên trung bình (giây)', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  avg_session_duration?: number;

  @ApiPropertyOptional({ description: 'Số lượt xem trang', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  page_views?: number;

  // Financial Metrics
  @ApiPropertyOptional({ description: 'Tổng tiền gửi', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  total_deposits?: number;

  @ApiPropertyOptional({ description: 'Số lần gửi tiền', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  deposit_count?: number;

  @ApiPropertyOptional({ description: 'Số tiền gửi lần đầu', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  first_deposit_amount?: number;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền lần đầu', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  first_deposit_date?: string;

  @ApiPropertyOptional({ description: 'Ngày gửi tiền cuối', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  last_deposit_date?: string;

  @ApiPropertyOptional({ description: 'Số ngày để gửi tiền lần đầu', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  days_to_first_deposit?: number;

  // Geographic & Demographic Data
  @ApiPropertyOptional({ description: 'Quốc gia' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  country?: string;

  @ApiPropertyOptional({ description: 'Thành phố' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  city?: string;

  @ApiPropertyOptional({ enum: GeographicRegion, description: 'Khu vực địa lý' })
  @IsOptional()
  @IsEnum(GeographicRegion)
  region?: GeographicRegion;

  @ApiPropertyOptional({ description: 'Nhóm tuổi' })
  @IsOptional()
  @IsString()
  @Length(1, 10)
  age_group?: string;

  @ApiPropertyOptional({ description: 'Giới tính' })
  @IsOptional()
  @IsString()
  @Length(1, 20)
  gender?: string;

  @ApiPropertyOptional({ description: 'Ngôn ngữ ưa thích' })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  preferred_language?: string;

  @ApiPropertyOptional({ description: 'Múi giờ' })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  timezone?: string;

  // Communication Preferences
  @ApiPropertyOptional({ description: 'Thông báo email', default: true })
  @IsOptional()
  @IsBoolean()
  email_notifications?: boolean;

  @ApiPropertyOptional({ description: 'Thông báo SMS', default: true })
  @IsOptional()
  @IsBoolean()
  sms_notifications?: boolean;

  @ApiPropertyOptional({ description: 'Thông báo push', default: false })
  @IsOptional()
  @IsBoolean()
  push_notifications?: boolean;

  @ApiPropertyOptional({ description: 'Email marketing', default: false })
  @IsOptional()
  @IsBoolean()
  marketing_emails?: boolean;

  // Risk & Compliance
  @ApiPropertyOptional({ description: 'Điểm rủi ro', minimum: 0, maximum: 100, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  @Type(() => Number)
  risk_score?: number;

  @ApiPropertyOptional({ description: 'KYC đã xác minh', default: false })
  @IsOptional()
  @IsBoolean()
  kyc_verified?: boolean;

  @ApiPropertyOptional({ description: 'Ngày xác minh KYC', type: 'string', format: 'date' })
  @IsOptional()
  @IsDateString()
  kyc_verified_date?: string;

  @ApiPropertyOptional({ description: 'Hoạt động đáng nghi', default: false })
  @IsOptional()
  @IsBoolean()
  suspicious_activity?: boolean;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  notes?: string;

  // Attribution & Campaign Data
  @ApiPropertyOptional({ description: 'UTM Source' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  utm_source?: string;

  @ApiPropertyOptional({ description: 'UTM Medium' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  utm_medium?: string;

  @ApiPropertyOptional({ description: 'UTM Campaign' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  utm_campaign?: string;

  @ApiPropertyOptional({ description: 'UTM Term' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  utm_term?: string;

  @ApiPropertyOptional({ description: 'UTM Content' })
  @IsOptional()
  @IsString()
  @Length(1, 255)
  utm_content?: string;

  @ApiPropertyOptional({ description: 'Chi phí thu hút khách hàng', minimum: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  acquisition_cost?: number;

  @ApiPropertyOptional({ description: 'ID phòng ban' })
  @IsOptional()
  @IsUUID()
  department_id?: string;
} 