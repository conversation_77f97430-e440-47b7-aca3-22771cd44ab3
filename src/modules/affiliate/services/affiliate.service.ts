import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual, LessThanOrEqual, In } from 'typeorm';
import { Affiliate } from '@/entities/affiliate.entity';
import { User } from '@/entities/user.entity';
import { AffiliateUsage, AffiliateUsageType, AffiliateUsageStatus } from '@/entities/affiliate-usage.entity';
import { CustomLoggerService } from '@/common/logger/logger.service';
import { LoggerIntegrationUtil } from '@/common/utils/logger-integration.util';
import { AuditLogService, AuditLog } from '@/common/services/audit-log.service';
import { AuditAction } from '@/entities/audit-log.entity';
import {
  AffiliateReportQueryDto,
  AffiliateReportResponseDto,
  AffiliateReportSummaryDto,
  AffiliatePerformanceDto,
  AffiliateRevenueDto,
  AffiliateConversionDto,
  AffiliateReportPeriod,
  AffiliateReportType,
} from '../dto/affiliate-report.dto';

export interface CreateAffiliateDto {
  code?: string;
  description?: string;
  commissionRate?: number;
  category?: string;
  expiresAt?: Date;
  maxUsageLimit?: number;
  metadata?: Record<string, any>;
}

export interface UpdateAffiliateDto {
  description?: string;
  commissionRate?: number;
  category?: string;
  expiresAt?: Date;
  maxUsageLimit?: number;
  metadata?: Record<string, any>;
}

export interface AffiliateStatsDto {
  totalUsageCount: number;
  successfulReferrals: number;
  totalCommissionEarned: number;
  conversionRate: number;
  isActive: boolean;
  isExpired: boolean;
  isUsageLimitReached: boolean;
  canBeUsed: boolean;
}

@Injectable()
export class AffiliateService {
  private serviceLogger: ReturnType<typeof LoggerIntegrationUtil.createServiceLogger>;

  constructor(
    @InjectRepository(Affiliate)
    private readonly affiliateRepository: Repository<Affiliate>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AffiliateUsage)
    private readonly affiliateUsageRepository: Repository<AffiliateUsage>,
    private readonly logger: CustomLoggerService,
    private readonly auditLogService: AuditLogService,
  ) {
    this.serviceLogger = LoggerIntegrationUtil.createServiceLogger(this.logger, 'AffiliateService');
  }

  /**
   * Create affiliate code for a user
   */
  @AuditLog({
    action: AuditAction.CREATE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[1], // userId
    riskLevel: 'medium',
    isSensitive: true,
  })
  async createAffiliate(userId: string, createDto: CreateAffiliateDto): Promise<Affiliate> {
    this.serviceLogger.logCreate('affiliate', { userId, ...createDto });

    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        this.serviceLogger.logNotFound('user', userId);
        throw new NotFoundException('User not found');
      }

      // Generate code if not provided
      let code = createDto.code;
      if (!code) {
        code = await this.generateUniqueCode(user.username);
      } else {
        // Check if custom code already exists
        const existingAffiliate = await this.affiliateRepository.findOne({
          where: { code },
        });
        if (existingAffiliate) {
          this.serviceLogger.logConflict('create affiliate', `Code already exists: ${code}`);
          throw new ConflictException('Affiliate code already exists');
        }
      }

      // Create affiliate
      const affiliate = this.affiliateRepository.create({
        code,
        userId,
        description: createDto.description,
        commissionRate: createDto.commissionRate || 0,
        category: createDto.category || 'general',
        expiresAt: createDto.expiresAt,
        maxUsageLimit: createDto.maxUsageLimit,
        metadata: createDto.metadata,
        isActive: true,
        activatedAt: new Date(),
      });

      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      this.serviceLogger.logCreateSuccess('affiliate', savedAffiliate.id, {
        code: savedAffiliate.code,
        userId,
      });

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logCreateError('affiliate', error, { userId, ...createDto });
      throw error;
    }
  }

  /**
   * Update affiliate
   */
  @AuditLog({
    action: AuditAction.UPDATE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[0], // affiliateId
    riskLevel: 'medium',
    isSensitive: true,
  })
  async updateAffiliate(affiliateId: string, updateDto: UpdateAffiliateDto): Promise<Affiliate> {
    this.serviceLogger.logUpdate('affiliate', affiliateId, updateDto);

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      // Store old values for audit
      const oldValues = { ...affiliate };

      // Update affiliate
      Object.assign(affiliate, updateDto);
      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      // Log audit
      await this.auditLogService.logCrudOperation(
        AuditAction.UPDATE,
        'Affiliate',
        affiliateId,
        undefined,
        oldValues,
        savedAffiliate
      );

      this.serviceLogger.logUpdateSuccess('affiliate', affiliateId, updateDto);

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logUpdateError('affiliate', affiliateId, error, updateDto);
      throw error;
    }
  }

  /**
   * Toggle affiliate status
   */
  async toggleAffiliateStatus(affiliateId: string, isActive: boolean): Promise<Affiliate> {
    this.serviceLogger.logUpdate('affiliate status', affiliateId, { isActive });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      const oldValues = { isActive: affiliate.isActive };

      affiliate.isActive = isActive;
      affiliate.activatedAt = isActive ? new Date() : affiliate.activatedAt;
      affiliate.deactivatedAt = !isActive ? new Date() : undefined;

      const savedAffiliate = await this.affiliateRepository.save(affiliate);

      // Log audit
      await this.auditLogService.logCrudOperation(
        AuditAction.UPDATE,
        'Affiliate',
        affiliateId,
        undefined,
        oldValues,
        { isActive: savedAffiliate.isActive }
      );

      this.serviceLogger.logUpdateSuccess('affiliate status', affiliateId, { isActive });

      return savedAffiliate;
    } catch (error) {
      this.serviceLogger.logUpdateError('affiliate status', affiliateId, error, { isActive });
      throw error;
    }
  }

  /**
   * Find affiliate by code
   */
  async findByCode(code: string): Promise<Affiliate | null> {
    this.serviceLogger.logFind('affiliate by code', { code });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { code },
        relations: ['user'],
      });

      if (affiliate) {
        this.serviceLogger.logFindSuccess('affiliate by code', 1, { code });
      } else {
        this.serviceLogger.logNotFound('affiliate', code);
      }

      return affiliate;
    } catch (error) {
      this.logger.error(
        `Failed to find affiliate by code: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get affiliate statistics
   */
  async getAffiliateStats(affiliateId: string): Promise<AffiliateStatsDto> {
    this.serviceLogger.logFind('affiliate stats', { affiliateId });

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      const stats: AffiliateStatsDto = {
        totalUsageCount: affiliate.totalUsageCount,
        successfulReferrals: affiliate.successfulReferrals,
        totalCommissionEarned: affiliate.totalCommissionEarned,
        conversionRate: affiliate.conversionRate,
        isActive: affiliate.isActive,
        isExpired: affiliate.isExpired,
        isUsageLimitReached: affiliate.isUsageLimitReached,
        canBeUsed: affiliate.canBeUsed,
      };

      this.serviceLogger.logFindSuccess('affiliate stats', 1, { affiliateId });

      return stats;
    } catch (error) {
      this.logger.error(
        `Failed to get affiliate stats: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get user's affiliates
   */
  async getUserAffiliates(userId: string): Promise<Affiliate[]> {
    this.serviceLogger.logFind('user affiliates', { userId });

    try {
      const affiliates = await this.affiliateRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      this.serviceLogger.logFindSuccess('user affiliates', affiliates.length, { userId });

      return affiliates;
    } catch (error) {
      this.logger.error(
        `Failed to get user affiliates: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Get referred users by affiliate
   */
  async getReferredUsers(affiliateId: string, page: number = 1, limit: number = 10): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.serviceLogger.logFind('referred users', { affiliateId, page, limit });

    try {
      const [users, total] = await this.userRepository.findAndCount({
        where: { affiliates: { id: affiliateId } },
        select: ['id', 'username', 'fullName', 'email', 'createdAt'],
        skip: (page - 1) * limit,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      this.serviceLogger.logFindSuccess('referred users', total, { affiliateId, page, limit });

      return {
        users,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get referred users: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Process referral when user registers
   */
  async processReferral(newUserId: string, affiliateCode: string): Promise<void> {
    this.serviceLogger.logCreate('referral processing', { newUserId, affiliateCode });

    try {
      const affiliate = await this.findByCode(affiliateCode);
      if (!affiliate || !affiliate.canBeUsed) {
        this.logger.warn(
          `Invalid or unusable affiliate code: ${affiliateCode}`,
          'AffiliateService'
        );
        return;
      }

      // Update user with affiliate reference
      await this.userRepository.update(newUserId, {
        affiliates: [{ id: affiliate.id, isActive: true }],
      });

      this.serviceLogger.logCreateSuccess('referral processing', newUserId, {
        affiliateCode,
        affiliateId: affiliate.id,
      });
    } catch (error) {
      this.serviceLogger.logCreateError('referral processing', error, { newUserId, affiliateCode });
      throw error;
    }
  }

  /**
   * Delete affiliate
   */
  @AuditLog({
    action: AuditAction.DELETE,
    entityType: 'Affiliate',
    getEntityId: (args) => args[0], // affiliateId
    riskLevel: 'high',
    isSensitive: true,
  })
  async deleteAffiliate(affiliateId: string): Promise<void> {
    this.serviceLogger.logDelete('affiliate', affiliateId);

    try {
      const affiliate = await this.affiliateRepository.findOne({
        where: { id: affiliateId },
      });

      if (!affiliate) {
        this.serviceLogger.logNotFound('affiliate', affiliateId);
        throw new NotFoundException('Affiliate not found');
      }

      // Check if affiliate has been used
      const usageCount = await this.affiliateUsageRepository.count({
        where: { affiliateId },
      });

      if (usageCount > 0) {
        throw new BadRequestException('Cannot delete affiliate that has been used');
      }

      await this.affiliateRepository.remove(affiliate);

      this.serviceLogger.logDeleteSuccess('affiliate', affiliateId);
    } catch (error) {
      this.serviceLogger.logDeleteError('affiliate', affiliateId, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive affiliate report
   */
  async generateAffiliateReport(query: AffiliateReportQueryDto): Promise<AffiliateReportResponseDto> {
    this.serviceLogger.logFind('affiliate report', query);

    try {
      const { startDate, endDate, period, reportType, affiliateCode, userId, category, status } = query;

      // Calculate date range
      const dateRange = this.calculateDateRange(startDate, endDate, period);
      
      // Build where conditions
      const whereConditions: any = {};
      if (affiliateCode) whereConditions.code = affiliateCode;
      if (userId) whereConditions.userId = userId;
      if (category) whereConditions.category = category;
      if (status) {
        whereConditions.isActive = status === 'active';
      }

      // Get affiliates with usage data
      const affiliates = await this.affiliateRepository.find({
        where: whereConditions,
        relations: ['user'],
      });

      // Get usage data for the period
      const affiliateIds = affiliates.map(a => a.id);
      const usageData = await this.affiliateUsageRepository.find({
        where: {
          createdAt: Between(dateRange.start, dateRange.end),
          affiliateId: In(affiliateIds),
        },
        relations: ['affiliate'],
      });

      // Generate summary
      const summary = await this.generateReportSummary(affiliates, usageData, dateRange);

      // Generate top performers
      const topPerformers = await this.generateTopPerformers(affiliates, usageData, dateRange);

      // Generate revenue breakdown
      const revenueBreakdown = await this.generateRevenueBreakdown(usageData, dateRange, period);

      // Generate conversion analysis
      const conversionAnalysis = await this.generateConversionAnalysis(affiliates, usageData);

      const report: AffiliateReportResponseDto = {
        summary,
        topPerformers,
        revenueBreakdown,
        conversionAnalysis,
        generatedAt: new Date(),
        period: period || AffiliateReportPeriod.MONTHLY,
        reportType: reportType || AffiliateReportType.SUMMARY,
      };

      this.serviceLogger.logFindSuccess('affiliate report', 1, query);

      return report;
    } catch (error) {
      this.logger.error(
        `Failed to generate affiliate report: ${error.message}`,
        error.stack,
        'AffiliateService'
      );
      throw error;
    }
  }

  /**
   * Calculate date range based on period
   */
  private calculateDateRange(
    startDate?: string,
    endDate?: string,
    period?: AffiliateReportPeriod
  ): { start: Date; end: Date } {
    const now = new Date();
    let start: Date;
    let end: Date = now;

    if (startDate && endDate) {
      start = new Date(startDate);
      end = new Date(endDate);
    } else {
      switch (period) {
        case AffiliateReportPeriod.DAILY:
          start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case AffiliateReportPeriod.WEEKLY:
          const dayOfWeek = now.getDay();
          const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
          start = new Date(now.getFullYear(), now.getMonth(), diff);
          break;
        case AffiliateReportPeriod.MONTHLY:
          start = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case AffiliateReportPeriod.YEARLY:
          start = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          start = new Date(now.getFullYear(), now.getMonth(), 1);
      }
    }

    return { start, end };
  }

  /**
   * Generate report summary
   */
  private async generateReportSummary(
    affiliates: Affiliate[],
    usageData: AffiliateUsage[],
    dateRange: { start: Date; end: Date }
  ): Promise<AffiliateReportSummaryDto> {
    const totalAffiliates = affiliates.length;
    const activeAffiliates = affiliates.filter(a => a.isActive).length;
    const totalUsageCount = usageData.length;
    const successfulReferrals = usageData.filter(u => u.status === AffiliateUsageStatus.SUCCESS).length;
    const totalCommissionEarned = usageData.reduce((sum, u) => sum + (u.commissionAmount || 0), 0);
    const totalTransactionValue = usageData.reduce((sum, u) => sum + (u.transactionValue || 0), 0);
    const averageConversionRate = totalUsageCount > 0 ? (successfulReferrals / totalUsageCount) * 100 : 0;

    return {
      totalAffiliates,
      activeAffiliates,
      totalUsageCount,
      totalSuccessfulReferrals: successfulReferrals,
      totalCommissionEarned,
      averageConversionRate,
      totalTransactionValue,
      periodStart: dateRange.start,
      periodEnd: dateRange.end,
    };
  }

  /**
   * Generate top performers
   */
  private async generateTopPerformers(
    affiliates: Affiliate[],
    usageData: AffiliateUsage[],
    dateRange: { start: Date; end: Date }
  ): Promise<AffiliatePerformanceDto[]> {
    const affiliateUsageMap = new Map<string, AffiliateUsage[]>();
    
    // Group usage data by affiliate
    usageData.forEach(usage => {
      if (!affiliateUsageMap.has(usage.affiliateId)) {
        affiliateUsageMap.set(usage.affiliateId, []);
      }
      affiliateUsageMap.get(usage.affiliateId)!.push(usage);
    });

    const performers: AffiliatePerformanceDto[] = [];

    for (const affiliate of affiliates) {
      const usages = affiliateUsageMap.get(affiliate.id) || [];
      const usageCount = usages.length;
      const successfulReferrals = usages.filter(u => u.status === AffiliateUsageStatus.SUCCESS).length;
      const commissionEarned = usages.reduce((sum, u) => sum + (u.commissionAmount || 0), 0);
      const transactionValue = usages.reduce((sum, u) => sum + (u.transactionValue || 0), 0);
      const conversionRate = usageCount > 0 ? (successfulReferrals / usageCount) * 100 : 0;

      performers.push({
        affiliateId: affiliate.id,
        code: affiliate.code,
        description: affiliate.description || '',
        userId: affiliate.userId,
        username: affiliate.user?.username || '',
        usageCount,
        successfulReferrals,
        commissionEarned,
        transactionValue,
        conversionRate,
        commissionRate: affiliate.commissionRate,
        isActive: affiliate.isActive,
        category: affiliate.category,
      });
    }

    // Sort by commission earned descending
    return performers.sort((a, b) => b.commissionEarned - a.commissionEarned).slice(0, 10);
  }

  /**
   * Generate revenue breakdown
   */
  private async generateRevenueBreakdown(
    usageData: AffiliateUsage[],
    dateRange: { start: Date; end: Date },
    period?: AffiliateReportPeriod
  ): Promise<AffiliateRevenueDto[]> {
    const revenueMap = new Map<string, {
      commissionEarned: number;
      transactionValue: number;
      successfulReferrals: number;
      usageCount: number;
    }>();

    // Group by date
    usageData.forEach(usage => {
      const dateKey = usage.createdAt.toISOString().split('T')[0];
      const existing = revenueMap.get(dateKey) || {
        commissionEarned: 0,
        transactionValue: 0,
        successfulReferrals: 0,
        usageCount: 0,
      };

      existing.commissionEarned += usage.commissionAmount || 0;
      existing.transactionValue += usage.transactionValue || 0;
      existing.successfulReferrals += usage.status === AffiliateUsageStatus.SUCCESS ? 1 : 0;
      existing.usageCount += 1;

      revenueMap.set(dateKey, existing);
    });

    // Convert to array and sort by date
    return Array.from(revenueMap.entries())
      .map(([date, data]) => ({
        date,
        ...data,
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Generate conversion analysis
   */
  private async generateConversionAnalysis(
    affiliates: Affiliate[],
    usageData: AffiliateUsage[]
  ): Promise<AffiliateConversionDto[]> {
    const conversionMap = new Map<string, {
      totalUsageCount: number;
      successfulReferrals: number;
      conversionTimes: number[];
      countries: Map<string, number>;
      devices: Map<string, number>;
    }>();

    // Group usage data by affiliate
    usageData.forEach(usage => {
      if (!conversionMap.has(usage.affiliateId)) {
        conversionMap.set(usage.affiliateId, {
          totalUsageCount: 0,
          successfulReferrals: 0,
          conversionTimes: [],
          countries: new Map(),
          devices: new Map(),
        });
      }

      const data = conversionMap.get(usage.affiliateId)!;
      data.totalUsageCount += 1;
      
      if (usage.status === AffiliateUsageStatus.SUCCESS) {
        data.successfulReferrals += 1;
        if (usage.convertedAt && usage.createdAt) {
          const conversionTime = (usage.convertedAt.getTime() - usage.createdAt.getTime()) / (1000 * 60 * 60); // hours
          data.conversionTimes.push(conversionTime);
        }
      }

      // Track countries
      if (usage.countryCode) {
        const currentCount = data.countries.get(usage.countryCode) || 0;
        data.countries.set(usage.countryCode, currentCount + 1);
      }

      // Track devices
      if (usage.deviceType) {
        const currentCount = data.devices.get(usage.deviceType) || 0;
        data.devices.set(usage.deviceType, currentCount + 1);
      }
    });

    const analysis: AffiliateConversionDto[] = [];

    for (const affiliate of affiliates) {
      const data = conversionMap.get(affiliate.id);
      if (!data) continue;

      const conversionRate = data.totalUsageCount > 0 ? (data.successfulReferrals / data.totalUsageCount) * 100 : 0;
      const averageConversionTime = data.conversionTimes.length > 0 
        ? data.conversionTimes.reduce((sum, time) => sum + time, 0) / data.conversionTimes.length 
        : 0;

      // Get top countries
      const topCountries = Array.from(data.countries.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([country, count]) => ({ country, count }));

      // Get top devices
      const topDevices = Array.from(data.devices.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([device, count]) => ({ device, count }));

      analysis.push({
        affiliateId: affiliate.id,
        code: affiliate.code,
        totalUsageCount: data.totalUsageCount,
        successfulReferrals: data.successfulReferrals,
        conversionRate,
        averageConversionTime,
        topCountries,
        topDevices,
      });
    }

    return analysis.sort((a, b) => b.conversionRate - a.conversionRate);
  }

  private async generateUniqueCode(username: string): Promise<string> {
    const baseCode = `AFF-${username.toUpperCase()}-${new Date().getFullYear()}`;
    let counter = 1;
    let code = baseCode;

    while (await this.affiliateRepository.findOne({ where: { code } })) {
      code = `${baseCode}-${counter}`;
      counter++;
    }

    return code;
  }
}
