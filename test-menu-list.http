### Test Menu List API
@baseUrl = http://localhost:3000/api/v1
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao

### Get all menus with submenus
GET {{baseUrl}}/user-menu-assignments/menus
Authorization: Bearer {{token}}

###

### Test with curl command
### curl -X GET "http://localhost:3000/api/v1/user-menu-assignments/menus" \
###   -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.8bzCz0CF9lrcJbhNeG4D2E8gzzhPYgy6Ub6JPJoYcao"

### Test without authorization (should return 401)
GET {{baseUrl}}/user-menu-assignments/menus

###

### Test with invalid token (should return 401)
GET {{baseUrl}}/user-menu-assignments/menus
Authorization: Bearer invalid-token
